import * as AWS from '@aws-sdk/client-s3';
import { createError } from 'h3';

export default defineEventHandler(async (event) => {
	const { bucketName, path } = getQuery(event);

	const { AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY } = useRuntimeConfig();

	const s3CLient = new AWS.S3({
		region: 'us-west-2',
		credentials: {
			accessKeyId: AWS_ACCESS_KEY_ID as string,
			secretAccessKey: AWS_SECRET_ACCESS_KEY as string
		}
	});

	try {
		const data = await s3CLient.listObjects({
			Bucket: bucketName as string,
			Delimiter: '/',
			Prefix: path as string
		});

		const tempList: Array<string> = [];
		// Use optional chaining in case CommonPrefixes is undefined
		const keys = data.CommonPrefixes?.map((obj: any) => obj.Prefix) || [];

		for (let index = 0; index < keys.length; index++) {
			const element = keys[index];

			// Expressão regular para encontrar "backup/store/" no início da string e "/" do final
			const regex = /^backup\/store\/|\/$/g;

			// Substituir o prefixo encontrado pela string vazia
			const result = element.replace(regex, '');
			tempList.push(result);
		}

		// On success, return the data directly. Nitro handles JSON serialization.
		return {
			message: 'Template list loaded successfully',
			templateList: tempList
		};
	} catch (err: any) {
		// On error, throw a proper error that Nitro can handle.
		throw createError({
			statusCode: 500,
			statusMessage: 'Failed to load template list from S3',
			data: {
				originalError: err.message
			}
		});
	}
});
