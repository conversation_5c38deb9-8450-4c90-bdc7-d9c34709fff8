/* eslint-disable no-console */
import {
	defineEvent<PERSON><PERSON><PERSON>,
	createError,
	readBody,
	setResponseHeaders
} from 'h3';
import { GoogleGenAI, Type } from '@google/genai';
import axios from 'axios';

interface GeminiChatPayload {
	systemInstruction: string;
	userPrompt: Array<{
		role: string;
		parts: { text: string }[];
	}>;
	customInstructions: string;
	model: string;
	googleSearchGrounding?: boolean;
	codeExecutionEnabled?: boolean;
	files?: any[];
	// Parâmetros para integração com banco
	jdbcConfigId?: number;
	baseUrl?: string;
	nuxtBaseUrl?: string;
	nuxtToken?: string;
	isToUseUserIaCredit?: boolean;
	selectedWorkspaceId: number;
	authorizationToken?: string;
	configChain?: {
		isMobileView: boolean;
		selectedAIMode: string;
		isFreeDb: boolean;
		isValidacao: boolean;
		isPlayground: boolean;
		variables: {
			today: string;
			customInstructions: string;
			dbDriverType: string;
		};
		currentUserToolSchema?: any;
		allowLilaToExecuteActions?: boolean;
		guidelinesToolsActions?: string;
		isFinalUserChatSideBar?: boolean;
		freeIaCreditsUsage: number;
		selectedScreenId: number;
	};
}

// Usar o tipo original do Gemini para compatibilidade
type StreamChunk = any;

// Função para obter API key do Gemini (hardcoded)
async function getGeminiApiKey(
	requestUrl: string,
	userToken: string,
	isToUseUserIaCredit: boolean,
	workspaceId: number,
	freeIaCreditsUsage: number
): Promise<string> {
	// console.log('🔑 Teste chamada gemini get key');
	// console.log('🔑 requestUrl:', requestUrl);
	// console.log('🔑 freeIaCreditsUsage:', freeIaCreditsUsage);

	const mitraEcpAxios = axios.create({
		baseURL: requestUrl,
		headers: {
			Accept: 'application/json',
			Authorization: userToken
		}
	});

	try {
		const userIaConfigResponse = await mitraEcpAxios.get(
			'/mitraspace/user/iaConfig'
		);
		const data = userIaConfigResponse.data;

		// const freeSuperIaRequestCount = data?.freeSuperIaRequestCount ?? 0;

		// console.log('freeSuperIaRequestCount:', freeSuperIaRequestCount);

		// ✅ Se ainda há créditos Super IA gratuitos
		if (freeIaCreditsUsage > 0) {
			console.log('🚀 freeSuperIaRequestCount > 0 — usando chave especial');
			return 'AIzaSyBva8WvYW_04qoSqGfbeUVvctnkSz_SOoE';
		}

		// ✅ Se deve usar os créditos do usuário
		if (isToUseUserIaCredit) {
			const model = data.model?.find((m: any) => m.id === 11);

			if (model?.modelKey) {
				console.log('✅ modelKey do ID 11 obtida com sucesso:', model.modelKey);
				return model.modelKey;
			} else {
				throw new Error('❌ modelKey com ID 11 não encontrada.');
			}
		} else {
			// ✅ Se não usa créditos do usuário, consulta por workspace
			const workspaceResponse = await mitraEcpAxios.get(
				`/mitraspace/iaConfig/${workspaceId}/11`
			);

			const modelKey = workspaceResponse.data?.openaiAccessKey;

			if (modelKey) {
				console.log(
					'✅ modelKey do ID 11 obtida com sucesso (por workspace):',
					modelKey
				);
				return modelKey;
			} else {
				throw new Error('❌ openaiAccessKey do workspace não encontrada.');
			}
		}
	} catch (error) {
		console.error('❌ Erro ao buscar modelKey:', error);
		throw error;
	}
}

// Função para buscar CHAIN do Mitra API
async function getChainFromMitraAPI(configChain: any): Promise<string> {
	// BASEURL e Authorization hardcoded de um projeto existente no Mitra em https://app.mitralab.io/w/9300/p/14458/
	const mitraAxios = axios.create({
		baseURL: 'https://api.mitrasheet.com:5541/rest/v0',
		headers: {
			'Content-Type': 'application/json',
			Authorization:
				'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDaGFpbiBMaWxhIiwiWC1UZW5hbnRJRCI6InRlbmFudF8xNDQ1OCJ9.uj9asFqEeIuoSWiye0QE1XIaGiYu2FhBvP44fP9hsxowE--VRG_NZi1v3DErew_uoQZ5icZsdCN-ucpbT2bJCg'
		}
	});

	let chainEndpoint = '';

	if (configChain.isMobileView) {
		chainEndpoint = '/MOBILE_MAPPED_QA';
	} else if (
		configChain.selectedAIMode === 'AI_ANALYST' &&
		!configChain.isFreeDb
	) {
		chainEndpoint = configChain.isValidacao ? '/ANALYST_QA' : '/Chain AIAnalystTAG';
	} else if (configChain.isFreeDb) {
		chainEndpoint = configChain.isValidacao ? '/FREEDB_QA' : '/Chain FreeDBTAG';
	} else {
		chainEndpoint = configChain.isValidacao ? '/MAPPED_QA' : '/Chain MappedDBTAG';
	}

	console.log(`🔗 CHAIN DEBUG - Buscando CHAIN do endpoint: ${chainEndpoint}`);

	try {
		const response = await mitraAxios.get(chainEndpoint);
		let chain = response.data;

		// Garantir que chain é uma string
		if (typeof chain !== 'string') {
			console.log(
				'🔗 CHAIN DEBUG - Resposta da API não é string, convertendo:',
				typeof chain
			);
			chain = JSON.stringify(chain);
		}

		// Aplicar transformações de variáveis
		if (configChain.variables) {
			chain = Object.entries(configChain.variables).reduce(
				(currentChain: string, [key, value]) => {
					const regex = new RegExp(`\\$\\{?${key}\\}?`, 'g');
					return currentChain.replace(regex, String(value));
				},
				chain
			);
		}

		// Aplicar transformação para playground se necessário
		if (configChain.isPlayground && configChain.selectedAIMode === 'AI_ANALYST') {
			chain = transformAddTelasInAddDash(chain);
		}

		console.log(
			`✅ CHAIN DEBUG - CHAIN obtida com sucesso (${chain.length} chars)`
		);
		return chain;
	} catch (error) {
		console.error('❌ CHAIN DEBUG - Erro ao buscar CHAIN:', error);
		throw error;
	}
}

// Função para transformar ADD_TELAS em ADD_DASHBOARD (migrada do client-side)
function transformAddTelasInAddDash(chain: string): string {
	let modifiedChain = chain;

	// Step 1: Rename top-level keys
	modifiedChain = modifiedChain.replaceAll('"ADD_TELAS"', '"ADD_DASHBOARD"');
	modifiedChain = modifiedChain.replaceAll('"ALT_TELAS"', '"ALT_DASHBOARD"');
	modifiedChain = modifiedChain.replaceAll(
		'"DELETE_TELAS"',
		'"DELETE_DASHBOARD"'
	);

	// Step 2: Transform ADD_DASHBOARD objects
	const addObjectRegex = /\{\s*"name":\s*([^,}]+),\s*"height":\s*([^}]+)\s*\}/g;
	modifiedChain = modifiedChain.replace(
		/"ADD_DASHBOARD":\s*\[([^\]]*)\]/g,
		(_match: any, arrayContent: any) => {
			const transformedObjects = arrayContent.replace(
				addObjectRegex,
				(_objMatch: any, nameVal: any, heightVal: any) => {
					return `{\n             "alias": ${nameVal},\n             "height": ${heightVal}\n         }`;
				}
			);
			return `"ADD_DASHBOARD": [${transformedObjects}]`;
		}
	);

	// Step 3: Transform ALT_DASHBOARD objects
	const altObjectRegex =
		/\{\s*"id":\s*([^,}]+),\s*"name":\s*([^,}]+),\s*"height":\s*([^}]+)\s*\}/g;
	modifiedChain = modifiedChain.replace(
		/"ALT_DASHBOARD":\s*\[([^\]]*)\]/g,
		(_match: any, arrayContent: any) => {
			const transformedObjects = arrayContent.replace(
				altObjectRegex,
				(_objMatch: any, idVal: any, nameVal: any, heightVal: any) => {
					return `{\n             "id": ${idVal},\n             "alias": ${nameVal},\n             "height": ${heightVal}\n         }`;
				}
			);
			return `"ALT_DASHBOARD": [${transformedObjects}]`;
		}
	);

	return modifiedChain;
}

export default defineEventHandler(async (event) => {
	// Retornar para requisições OPTIONS
	if (event.node.req.method === 'OPTIONS') {
		setResponseHeaders(event, {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization'
		});
		return { success: true };
	}

	try {
		const payload: GeminiChatPayload = await readBody(event);

		if (!payload.authorizationToken) {
			throw createError({
				statusCode: 400,
				statusMessage: 'Token de autorização é obrigatório'
			});
		}

		const baseUrl = payload.nuxtBaseUrl || 'https://api0.mitraecp.com:1005';
		const payloadUserToken = payload.nuxtToken ?? payload.authorizationToken;
		const isToUseUserIaCredit = payload.isToUseUserIaCredit ?? true;

		// Obter API key do Gemini (hardcoded)
		const geminiApiKey = await getGeminiApiKey(
			baseUrl,
			payloadUserToken,
			isToUseUserIaCredit,
			payload.selectedWorkspaceId,
			payload.configChain?.freeIaCreditsUsage ?? 0
		);

		console.log('🔑 Usando API key do Gemini');
		console.log('🔑 geminiApiKey:', geminiApiKey);

		// Inicializar Google GenAI
		const ai = new GoogleGenAI({
			apiKey: geminiApiKey
		});

		// Headers CORS básicos (streaming será configurado depois)
		setResponseHeaders(event, {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization'
		});

		// Função para limpar conteúdos (baseada no useGeminiAI.ts)
		const cleanContents = (contents: any[]) => {
			return contents
				.map((content) => {
					if (content.parts) {
						const cleanedParts = content.parts
							.filter((part: any) => {
								// Manter partes com texto válido OU com inlineData (imagens)
								return (part.text && part.text.trim() !== '') || part.inlineData;
							})
							.map((part: any) => {
								// Se tem inlineData (imagem), manter como está
								if (part.inlineData) {
									return part;
								}
								// Se tem texto, limpar o texto
								if (part.text) {
									return {
										text: part.text.replace(/\n\s*\n/g, '\n').trim()
									};
								}
								return part;
							});

						return {
							...content,
							parts: cleanedParts
						};
					}
					return content;
				})
				.filter((content) => content.parts && content.parts.length > 0);
		};

		// Preparar conteúdos para o Gemini
		const contents = [
			...payload.userPrompt,
			{
				role: 'user',
				parts: [{ text: payload.customInstructions }]
			}
		];

		// Integrar arquivos no último prompt do usuário (se houver)
		if (payload.files && payload.files.length > 0) {
			// Encontrar o último prompt do usuário
			const lastUserContentIndex = contents
				.map((c, i) => ({ content: c, index: i }))
				.filter(({ content }) => content.role === 'user')
				.pop()?.index;

			if (lastUserContentIndex !== undefined) {
				const lastUserContent = contents[lastUserContentIndex];

				// Processar cada arquivo (já vem em base64 do cliente)
				for (const file of payload.files) {
					if (file && file.type && file.data) {
						try {
							// Validar formato base64
							const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
							const isValidBase64 = base64Pattern.test(file.data) && file.data.length > 0;

							console.log(`📎 GEMINI FILE DEBUG - Processando arquivo: ${file.name}`);
							console.log(`📋 GEMINI FILE DEBUG - Tipo: ${file.type}`);
							console.log(`📏 GEMINI FILE DEBUG - Tamanho base64: ${file.data.length} chars`);
							console.log(`✅ GEMINI FILE DEBUG - Base64 válido: ${isValidBase64}`);

							if (!isValidBase64) {
								console.error(`❌ GEMINI FILE DEBUG - Base64 inválido para ${file.name}`);
								continue;
							}

							// Verificar tipos de arquivo suportados pelo Gemini
							const supportedTypes = [
								// Imagens
								'image/png',
								'image/jpeg',
								'image/jpg',
								'image/webp',
								'image/gif',
								// Documentos
								'application/pdf',
								'text/plain',
								'text/csv',
								'text/html',
								'text/css',
								'text/javascript',
								// Documentos Office
								'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
								'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
								'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
								// Outros
								'application/rtf',
								'text/rtf'
							];

							if (supportedTypes.includes(file.type)) {
								(lastUserContent.parts as any).push({
									inlineData: {
										data: file.data,
										mimeType: file.type
									}
								});

								console.log(
									`✅ GEMINI FILE DEBUG - Arquivo adicionado com sucesso: ${file.name} (${file.type})`
								);
							} else {
								console.warn(
									`⚠️ GEMINI FILE DEBUG - Tipo de arquivo não suportado: ${file.type} para ${file.name}`
								);
							}
						} catch (error) {
							console.error('❌ GEMINI FILE DEBUG - Erro ao processar arquivo:', error);
						}
					} else {
						console.log(
							`📎 GEMINI FILE DEBUG - Arquivo inválido ou sem dados: ${
								file?.name || 'unknown'
							}`
						);
					}
				}
			}
		}

		const cleanedContents = cleanContents(contents);

		// Log detalhado dos contents para debug de visão
		console.log('🔍 GEMINI VISION DEBUG - Contents ANTES do cleanContents:');
		console.log(JSON.stringify(contents, null, 2));
		console.log('🔍 GEMINI VISION DEBUG - Contents DEPOIS do cleanContents:');
		console.log(JSON.stringify(cleanedContents, null, 2));

		// Verificar se há arquivos (imagens/documentos) nos contents
		const hasFiles = cleanedContents.some(
			(content) => content.parts && content.parts.some((part: any) => part.inlineData)
		);
		console.log(`� GEMINI FILE DEBUG - Contém arquivos: ${hasFiles}`);

		if (hasFiles) {
			console.log(`🤖 GEMINI FILE DEBUG - Modelo usado: ${payload.model}`);
			console.log('📋 GEMINI FILE DEBUG - Verificando se modelo suporta arquivos...');

			// Verificar se o modelo suporta arquivos (visão + documentos)
			const fileSupportedModels = [
				'gemini-2.5-pro-preview-06-05',
				'gemini-2.5-flash-preview-05-20',
				'gemini-2.0-flash-exp',
				'gemini-1.5-pro',
				'gemini-1.5-flash'
			];

			const supportsFiles = fileSupportedModels.some((model) =>
				payload.model.includes(model)
			);
			console.log(`� GEMINI FILE DEBUG - Modelo suporta arquivos: ${supportsFiles}`);

			if (!supportsFiles) {
				console.warn(
					`⚠️ GEMINI FILE DEBUG - ATENÇÃO: Modelo ${payload.model} pode não suportar arquivos!`
				);
			}

			// Listar tipos de arquivos detectados
			const fileTypes = new Set<string>();
			cleanedContents.forEach((content) => {
				if (content.parts) {
					content.parts.forEach((part: any) => {
						if (part.inlineData && part.inlineData.mimeType) {
							fileTypes.add(part.inlineData.mimeType);
						}
					});
				}
			});
			console.log(
				`📋 GEMINI FILE DEBUG - Tipos de arquivo detectados: ${Array.from(
					fileTypes
				).join(', ')}`
			);
		}

		// Configurar tools baseado nas configurações (EXATO do useGeminiAI.ts)
		// Tool exclusivity rules:
		// 1. Code Execution + Google Search Grounding = Compatible ✅
		// 2. Code Execution + Function Calling (SQL) = Incompatible ❌
		// 3. Google Search Grounding + Function Calling (SQL) = Incompatible ❌
		const tools: any[] = [];

		// Add Google Search grounding if enabled
		if (payload.googleSearchGrounding) {
			tools.push({
				googleSearch: {}
			});
			console.log('🔍 Google Search grounding enabled');
		}

		// Add Code Execution if enabled (compatible with Google Search)
		if (payload.codeExecutionEnabled) {
			tools.push({
				codeExecution: {}
			});
			console.log('🐍 Code Execution enabled');
		}

		// Add SQL function calling only if neither Google Search nor Code Execution is enabled
		if (!payload.googleSearchGrounding && !payload.codeExecutionEnabled) {
			tools.push({
				functionDeclarations: [
					{
						name: 'run_sql',
						description:
							'Execute SQL queries on the database to retrieve or manipulate data. Use this when you need to get specific information from the database.',
						parameters: {
							type: Type.OBJECT,
							properties: {
								query: {
									type: Type.STRING,
									description: 'The SQL query to execute, e.g. SELECT * FROM users WHERE id = 1'
								},
								jdbcConfigId: {
									type: Type.NUMBER,
									description: 'The JDBC config ID to use for the query'
								}
							},
							required: ['query', 'jdbcConfigId']
						}
					},
					{
						name: 'run_actions',
						description:
							'Executes a specific action by name, optionally using key/value variables and user-provided instructions.',
						parameters: {
							type: Type.OBJECT,
							properties: {
								actionName: {
									type: Type.STRING,
									description: 'The exact name of the action to execute.'
								},
								variables: {
									type: Type.OBJECT,
									description:
										'Key-value pairs representing input variables for the action. Example: { ":VAR_USUARIOGUEST": "john.doe", ":VAR_DT_FIM": "2025-07-01" }'
								}
							},
							required: ['actionName']
						}
					}
				]
			});
			console.log('🗄️ SQL function calling enabled');
		} else if (payload.googleSearchGrounding || payload.codeExecutionEnabled) {
			console.log(
				'🚫 SQL function calling disabled (incompatible with Google Search or Code Execution)'
			);
		}

		// Buscar CHAIN do Mitra API se configChain estiver presente
		let systemInstruction = payload.systemInstruction;
		if (payload.configChain) {
			const { isFinalUserChatSideBar, guidelinesToolsActions } = payload.configChain;

			if (isFinalUserChatSideBar) {
				console.log(
					'⚠️ CHAIN BLOCKED - Ignorando CHAIN por isFinalUserChatSideBar=true'
				);
				const schema = payload.configChain?.currentUserToolSchema;
				const schemaString = schema ? JSON.stringify(schema, null, 2) : '';
				const baseInstruction = guidelinesToolsActions?.trim() || '';
				systemInstruction = `${baseInstruction}\n\nUser Tool Schema:\n${schemaString}`;
			} else {
				console.log('🔗 CHAIN DEBUG - Processando configChain no server-side');
				try {
					systemInstruction = await getChainFromMitraAPI(payload.configChain);
					console.log('✅ CHAIN DEBUG - CHAIN processada com sucesso no server-side');
				} catch (error) {
					console.error(
						'❌ CHAIN DEBUG - Erro ao processar CHAIN no server-side:',
						error
					);
				}
			}
		}

		// Se grounding está habilitado, adicionar instrução para forçar uso das informações
		if (payload.googleSearchGrounding) {
			systemInstruction += `\n\nIMPORTANTE SOBRE GROUNDING:
        1. Quando informações de busca na web estiverem disponíveis, você DEVE utilizá-las na sua resposta
        2. Sempre cite e referencie as informações encontradas nas buscas
        3. Para perguntas sobre notícias, eventos atuais, cotações, clima, etc., use OBRIGATORIAMENTE as informações das buscas
        4. Não responda apenas com seu conhecimento interno quando há informações atualizadas disponíveis
        5. Integre as informações das buscas de forma natural na resposta`;
		}

		const toolConfig = {
			systemInstruction,
			temperature: 0.01,
			tools,
			thinkingConfig: {
				includeThoughts: true
			}
		};

		// Fazer chamada inicial para o Gemini com streaming
		let initialResponse;
		try {
			console.log(
				`🚀 GEMINI VISION DEBUG - Iniciando chamada para modelo: ${payload.model}`
			);
			initialResponse = await ai.models.generateContentStream({
				model: payload.model,
				contents: cleanedContents,
				config: toolConfig
			});
			console.log('✅ GEMINI VISION DEBUG - Chamada iniciada com sucesso');
			// chave gemini
			console.log('chave gemini', geminiApiKey);

			// Configurar streaming APENAS após sucesso da chamada inicial
			setResponseHeaders(event, {
				'Content-Type': 'text/plain; charset=utf-8',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive'
			});

			event.node.res.writeHead(200, {
				'Content-Type': 'text/plain; charset=utf-8',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive'
			});
		} catch (error: any) {
			console.error('❌ GEMINI VISION DEBUG - Erro na chamada inicial:', error);
			console.error('📋 GEMINI VISION DEBUG - Detalhes do erro:', {
				message: error.message,
				status: error.status,
				statusText: error.statusText,
				model: payload.model,
				hasImages: cleanedContents.some(
					(content) => content.parts && content.parts.some((part: any) => part.inlineData)
				)
			});

			// Marcar erro como sendo do Gemini para o sistema de retry
			const geminiError = new Error(error.message || 'Erro do Gemini');
			(geminiError as any).isGeminiError = true;
			(geminiError as any).geminiErrorSource = 'server-side-gemini-chat.post.ts';

			// Retornar erro HTTP adequado para o client-side processar
			throw createError({
				statusCode: error.status || 500,
				statusMessage: `[GEMINI_ERROR] ${error.message || 'Erro na chamada do Gemini'}`,
				data: {
					isGeminiError: true,
					geminiErrorSource: 'server-side-gemini-chat.post.ts',
					originalError: error.message
				}
			});
		}

		// Processar stream de resposta
		const functionCalls: any[] = [];
		const modelParts: any[] = [];
		let groundingMetadata: any = null; // Capturar grounding metadata (igual ao useGeminiAI.ts)

		// Throttling para processamento de chunks (igual ao useGeminiAI.ts)
		let pendingChunks: string[] = [];
		let throttleTimeout: NodeJS.Timeout | null = null;
		const THROTTLE_DELAY = 300; // 300ms de delay para agrupar chunks (otimizado original)

		// Função para enviar chunk via stream no formato original do Gemini
		const sendChunk = (chunk: StreamChunk) => {
			const data = `data: ${JSON.stringify(chunk)}\n\n`;
			event.node.res.write(data);
			return data;
		};

		// Função para extrair objetos válidos do texto (baseada no useAnswerStreamProcessor.ts)
		const extractValidObjectsFromText = (text: string): any[] => {
			const objects: any[] = [];

			console.log(
				'🔍 EXTRACT DEBUG - Texto recebido:',
				text.substring(0, 300) + '...'
			);

			// Extrai conteúdo das tags BuildingSystem (completas e parciais)
			const buildingSystemRegex = /<BuildingSystem>([\s\S]*?)(<\/BuildingSystem>|$)/g;
			const matches = [...text.matchAll(buildingSystemRegex)];

			console.log(
				'� EXTRACT DEBUG - Matches BuildingSystem encontrados:',
				matches.length
			);

			if (matches.length === 0) {
				// Se não encontrou tags BuildingSystem, procura diretamente por arrays
				console.log('🔍 EXTRACT DEBUG - Procurando arrays diretamente no texto');
				return extractArraysDirectly(text);
			}

			for (const match of matches) {
				const content = match[1].trim();
				console.log(
					'🔍 EXTRACT DEBUG - Conteúdo BuildingSystem:',
					content.substring(0, 200) + '...'
				);

				// Procura por arrays de objetos usando TODAS as chaves do superAIPlanKeys
				const superAIPlanKeys = [
					'ADD_ACTIONS',
					'ADD_ATRIBUTES_DB',
					'ADD_COMPONENT_BUTTON',
					'ADD_COMPONENT_CONTAINER',
					'ADD_COMPONENT_CRUD_LIST',
					'ADD_COMPONENT_GRAPH',
					'ADD_COMPONENT_HTML',
					'ADD_COMPONENT_IMAGEM',
					'ADD_COMPONENT_IMAGE',
					'ADD_COMPONENT_PIECHART',
					'ADD_COMPONENT_BARCHART',
					'ADD_COMPONENT_INPUT',
					'ADD_COMPONENT_KANBAN',
					'ADD_COMPONENT_LABEL',
					'ADD_COMPONENT_LIST',
					'ADD_COMPONENT_REACT',
					'ADD_COMPONENT_GENERAL_LIST',
					'ADD_COMPONENT_GENERAL_KANBAN',
					'ADD_COMPONENT_SELECTOR',
					'ADD_COMPONENT_TABLE',
					'ADD_DBACTIONS',
					'ADD_DETAILS_MODAL',
					'ADD_FORMS',
					'ADD_TABELAS_DB',
					'ADD_CUBES_DB',
					'ADD_DASHBOARD',
					'ADD_TELAS',
					'ADD_VARIAVEIS',
					'ADD_WIDGET',
					'ALT_ACTIONS',
					'ALT_ATRIBUTES_DB',
					'ALT_COMPONENT_BUTTON',
					'ALT_COMPONENT_CONTAINER',
					'ALT_COMPONENT_CRUD_LIST',
					'ALT_COMPONENT_GRAPH',
					'ALT_COMPONENT_HTML',
					'ALT_COMPONENT_IMAGEM',
					'ALT_COMPONENT_IMAGE',
					'ALT_COMPONENT_PIECHART',
					'ALT_COMPONENT_BARCHART',
					'ALT_COMPONENT_INPUT',
					'ALT_COMPONENT_KANBAN',
					'ALT_COMPONENT_LABEL',
					'ALT_COMPONENT_LIST',
					'ALT_COMPONENT_REACT',
					'ALT_COMPONENT_GENERAL_LIST',
					'ALT_COMPONENT_GENERAL_KANBAN',
					'ALT_COMPONENT_SELECTOR',
					'ALT_COMPONENT_TABLE',
					'ALT_DETAILS_MODAL',
					'ALT_FORMS',
					'ALT_TABELAS_DB',
					'ALT_CUBES_DB',
					'ALT_DASHBOARD',
					'ALT_TELAS',
					'DELETE_ATRIBUTES_DB',
					'DELETE_COMPONENT',
					'DELETE_DBACTIONS',
					'DELETE_DETAILS_MODAL',
					'DELETE_FORMS',
					'DELETE_CUBES_DB',
					'DELETE_TABELAS_DB',
					'DELETE_TAB_DETAILS_MODAL',
					'DELETE_DASHBOARD',
					'DELETE_TELAS',
					'DELETE_VARIAVEIS',
					'RUN_DDL',
					'RUN_DML'
				];

				const arrayPatterns = superAIPlanKeys.map((key) => ({
					pattern: new RegExp(`"${key}":\\s*\\[([\\s\\S]*?)(\\]|$)`, 'g'),
					type: key
				}));

				for (const { pattern, type } of arrayPatterns) {
					const arrayMatches = [...content.matchAll(pattern)];
					console.log(`🔍 EXTRACT DEBUG - Matches para ${type}:`, arrayMatches.length);

					for (const arrayMatch of arrayMatches) {
						const arrayContent = arrayMatch[1];
						console.log(`� EXTRACT DEBUG - Conteúdo do array ${type}:`, arrayContent);

						// Extrai objetos individuais do array (completos e parciais)
						const extractedObjects = extractObjectsFromArrayContent(arrayContent, type);
						objects.push(...extractedObjects);
					}
				}
			}

			console.log('🔍 EXTRACT DEBUG - Total de objetos extraídos:', objects.length);
			return objects;
		};

		// Função auxiliar para extrair arrays diretamente do texto
		const extractArraysDirectly = (text: string): any[] => {
			const objects: any[] = [];
			const arrayPatterns = [
				{ pattern: /"ADD_TELAS":\s*\[([\s\S]*?)(\]|$)/g, type: 'ADD_TELAS' },
				{ pattern: /"ADD_DASHBOARD":\s*\[([\s\S]*?)(\]|$)/g, type: 'ADD_DASHBOARD' }
			];

			for (const { pattern, type } of arrayPatterns) {
				const matches = [...text.matchAll(pattern)];
				for (const match of matches) {
					const arrayContent = match[1];
					const extractedObjects = extractObjectsFromArrayContent(arrayContent, type);
					objects.push(...extractedObjects);
				}
			}

			return objects;
		};

		// Função auxiliar para extrair objetos de conteúdo de array
		const extractObjectsFromArrayContent = (
			arrayContent: string,
			arrayType: string
		): any[] => {
			const objects: any[] = [];

			// Procura por objetos completos primeiro
			const completeObjectMatches = arrayContent.match(/\{[^{}]*\}/g);
			if (completeObjectMatches) {
				for (const objStr of completeObjectMatches) {
					try {
						const parsed = JSON.parse(objStr);
						objects.push({
							...parsed,
							_arrayType: arrayType,
							_extractedAt: Date.now(),
							_isPartial: false
						});
						console.log(
							`✅ EXTRACT DEBUG - Objeto completo extraído de ${arrayType}:`,
							parsed
						);
					} catch (e) {
						console.log(`❌ EXTRACT DEBUG - Erro ao parsear objeto completo:`, objStr);
					}
				}
			}

			// Se não encontrou objetos completos, procura por objetos parciais
			if (objects.length === 0) {
				const partialObjectRegex = /\{\s*"[^"]+"\s*:\s*"[^"]*"/g;
				const partialMatches = [...arrayContent.matchAll(partialObjectRegex)];

				for (const match of partialMatches) {
					const partialObj = match[0];
					// Tenta completar o objeto parcial
					const attempts = [
						partialObj + '}',
						partialObj + '"}',
						partialObj + ', "height": 300}'
					];

					for (const attempt of attempts) {
						try {
							const parsed = JSON.parse(attempt);
							objects.push({
								...parsed,
								_arrayType: arrayType,
								_extractedAt: Date.now(),
								_isPartial: true
							});
							console.log(
								`✅ EXTRACT DEBUG - Objeto parcial extraído de ${arrayType}:`,
								parsed
							);
							break;
						} catch (e) {
							// Continua tentando
						}
					}
				}
			}

			return objects;
		};

		// Função para processar chunks throttled (baseada no useGeminiAI.ts)
		const processChunksThrottled = () => {
			if (pendingChunks.length === 0) return;

			// Junta todos os chunks pendentes em um único texto
			const combinedText = pendingChunks.join('');
			console.log(
				'🔄 THROTTLE DEBUG - Processando chunks combinados:',
				combinedText.substring(0, 200) + '...'
			);
			pendingChunks = [];

			// Processa o texto combinado para extrair objetos válidos
			const newValidObjects = extractValidObjectsFromText(combinedText);
			console.log('🎯 THROTTLE DEBUG - Objetos extraídos:', newValidObjects.length);

			// Se encontrou novos objetos válidos, envia via stream
			if (newValidObjects.length > 0) {
				console.log(
					'✅ THROTTLE DEBUG - Enviando objetos via stream:',
					newValidObjects
				);
				newValidObjects.forEach((obj) => {
					sendChunk({
						type: 'validObject',
						object: obj,
						timestamp: Date.now()
					});
				});
			}
		};

		// Função para adicionar chunk ao processamento throttled
		const addChunkToProcess = (text: string) => {
			console.log(
				'🔄 THROTTLE DEBUG - Adicionando chunk:',
				text.substring(0, 100) + '...'
			);
			pendingChunks.push(text);

			// Cancela o timeout anterior se existir
			if (throttleTimeout) {
				clearTimeout(throttleTimeout);
			}

			// Agenda o processamento após o delay
			throttleTimeout = setTimeout(processChunksThrottled, THROTTLE_DELAY);
		};

		// Processar resposta inicial - enviar response completo como o useGeminiAI.ts fazia
		for await (const response of initialResponse) {
			// Capturar grounding metadata se disponível (LOG TODOS OS CHUNKS)
			if (response.candidates?.[0]?.groundingMetadata) {
				const newMetadata = response.candidates[0].groundingMetadata;

				console.log('🔍 GROUNDING DEBUG - CHUNK RECEBIDO:', {
					hasGroundingSupports: !!newMetadata.groundingSupports,
					hasSearchEntryPoint: !!newMetadata.searchEntryPoint,
					hasWebSearchQueries: !!newMetadata.webSearchQueries,
					groundingSupportsCount: newMetadata.groundingSupports?.length || 0,
					webSearchQueriesCount: newMetadata.webSearchQueries?.length || 0,
					keys: Object.keys(newMetadata)
				});

				// SEMPRE substitui para ver todos os chunks
				groundingMetadata = newMetadata;
				console.log('🔍 GROUNDING DEBUG - Metadata atualizado (initial)');
			}

			// Enviar a resposta completa no formato original do Gemini (como era no useGeminiAI.ts)
			sendChunk(response);

			// Processar function calls e chunks de texto para execução no server-side
			if (response.candidates?.[0]?.content?.parts) {
				for (const part of response.candidates[0].content.parts) {
					if (part.functionCall) {
						functionCalls.push(part.functionCall);
						modelParts.push(part);
					} else if (part.text) {
						// Adicionar chunk de texto ao processamento throttled (igual ao useGeminiAI.ts)
						addChunkToProcess(part.text);
					}
				}
			}
		}

		// Processar function calls se existirem
		if (functionCalls.length > 0) {
			const functionCallMetadata = await processFunctionCalls(
				functionCalls,
				modelParts,
				ai,
				payload,
				cleanedContents,
				toolConfig,
				event,
				sendChunk,
				addChunkToProcess
			);

			// Se function calls retornaram grounding metadata mais completo, usar ele
			if (
				functionCallMetadata &&
				(!groundingMetadata || functionCallMetadata.groundingSupports)
			) {
				groundingMetadata = functionCallMetadata;
				console.log('🔍 GROUNDING DEBUG - Metadata de function calls usado como final');
			}
		}

		// Metadata de grounding já foi enviada junto com as respostas originais

		// Processa qualquer chunk pendente antes de finalizar (igual ao useGeminiAI.ts)
		if (throttleTimeout) {
			clearTimeout(throttleTimeout);
			throttleTimeout = null;
		}
		if (pendingChunks.length > 0) {
			processChunksThrottled();
		}

		// Enviar grounding metadata final se disponível (igual ao useGeminiAI.ts)
		if (groundingMetadata) {
			console.log('🔍 GROUNDING DEBUG - METADATA FINAL SENDO ENVIADO:');
			console.log('🔍 GROUNDING DEBUG - Resumo:', {
				hasGroundingSupports: !!groundingMetadata.groundingSupports,
				hasSearchEntryPoint: !!groundingMetadata.searchEntryPoint,
				hasWebSearchQueries: !!groundingMetadata.webSearchQueries,
				groundingSupportsCount: groundingMetadata.groundingSupports?.length || 0,
				webSearchQueriesCount: groundingMetadata.webSearchQueries?.length || 0,
				allKeys: Object.keys(groundingMetadata)
			});
			console.log(
				'🔍 GROUNDING DEBUG - Metadata completo:',
				JSON.stringify(groundingMetadata, null, 2)
			);

			// Se não tem groundingSupports, vamos investigar por que
			if (!groundingMetadata.groundingSupports) {
				console.log('⚠️ GROUNDING DEBUG - ATENÇÃO: Metadata sem groundingSupports!');
				console.log(
					'⚠️ GROUNDING DEBUG - Isso pode indicar que o Gemini não usou as informações do grounding na resposta'
				);
			}

			sendChunk({
				type: 'groundingMetadata',
				groundingMetadata,
				timestamp: Date.now()
			});
		} else {
			console.log('🔍 GROUNDING DEBUG - Nenhum metadata para enviar');
		}

		// Finalizar stream
		event.node.res.end();
	} catch (error: any) {
		console.error('Erro no endpoint Gemini:', error);

		// Se o erro já foi tratado e marcado como Gemini error, propagar
		if (
			error.statusCode &&
			error.statusMessage &&
			error.statusMessage.includes('[GEMINI_ERROR]')
		) {
			throw error;
		}

		// Caso contrário, tratar como erro geral
		throw createError({
			statusCode: error.statusCode || 500,
			statusMessage:
				error.statusMessage ||
				`[GENERAL_ERROR] ${error.message || 'Erro interno do servidor'}`,
			data: {
				isGeminiError: false,
				originalError: error.message
			}
		});
	}
});

// Função para processar function calls
async function processFunctionCalls(
	functionCalls: any[],
	modelParts: any[],
	ai: any,
	payload: GeminiChatPayload,
	cleanedContents: any[],
	toolConfig: any,
	event: any,
	sendChunk: (chunk: StreamChunk) => string,
	addChunkToProcess: (text: string) => void
): Promise<any> {
	console.log('🔧 Processando function calls:', functionCalls.length);

	let functionCallGroundingMetadata: any = null; // Capturar grounding metadata das function calls

	// Função real para execução de SQL (baseada no useGeminiAI.ts)
	const executeSQLFunction = async (query: string, jdbcConfigId?: number) => {
		console.log('🗄️ Executando SQL:', query);

		const finalJdbcConfigId = jdbcConfigId || payload.jdbcConfigId || 1;
		const baseUrl = payload.baseUrl || 'https://api0.mitraecp.com:1005';
		const authToken = payload.authorizationToken;

		if (!authToken) {
			return {
				success: false,
				error: 'Token de autorização não fornecido',
				query,
				jdbcConfigId: finalJdbcConfigId
			};
		}

		try {
			// Fazer a requisição para a API real
			const response = await fetch(`${baseUrl}/iaShortcuts/query`, {
				method: 'POST',
				headers: {
					Accept: 'application/json, text/plain, */*',
					Authorization: authToken,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ query, jdbcConfigId: finalJdbcConfigId })
			});

			if (!response.ok) {
				let errorBody = null;
				let errorMessage = `HTTP ${response.status} - ${response.statusText}`;

				try {
					const errorText = await response.text();
					if (errorText) {
						try {
							errorBody = JSON.parse(errorText);
							errorMessage = errorBody.message || errorBody.error || errorMessage;
						} catch {
							errorMessage = errorText;
						}
					}
				} catch (parseError) {
					console.warn('Could not parse error response:', parseError);
				}

				return {
					success: false,
					error: errorMessage,
					statusCode: response.status,
					statusText: response.statusText,
					errorBody,
					query,
					jdbcConfigId: finalJdbcConfigId
				};
			}

			const data = await response.json();
			return {
				success: true,
				data,
				query,
				jdbcConfigId: finalJdbcConfigId
			};
		} catch (error) {
			console.error('Error executing SQL query:', error);

			let errorMessage = 'Unknown error';
			let errorDetails = null;

			if (error instanceof Error) {
				errorMessage = error.message;
				errorDetails = {
					name: error.name,
					stack: error.stack
				};
			} else if (typeof error === 'string') {
				errorMessage = error;
			} else {
				errorDetails = error;
			}

			return {
				success: false,
				error: errorMessage,
				errorDetails,
				query,
				jdbcConfigId: finalJdbcConfigId,
				errorType: 'network_or_parsing_error'
			};
		}
	};

	const executeRunAction = async (
		actionName: string,
		variables: Record<string, any>
	) => {
		console.log('🛠️ Buscando action para executar:', actionName);

		const baseUrl = payload.baseUrl || 'https://api0.mitraecp.com:1005';
		const authToken = payload.authorizationToken;

		if (!authToken) {
			return {
				success: false,
				error: 'Token de autorização não fornecido',
				actionName
			};
		}

		// Pega as ações existentes no projeto
		const actionResponse = await fetch(`${baseUrl}/iaShortcuts/action/all`, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				Authorization: authToken,
				'Content-Type': 'application/json'
			}
		});

		if (!actionResponse.ok) {
			return {
				success: false,
				error: 'Erro ao carregar lista de ações.',
				actionName
			};
		}

		const actionsList = await actionResponse.json();
		const screenId = payload.configChain?.selectedScreenId;
		const allowedActions =
			payload.configChain?.currentUserToolSchema?.CALL_ACTION || [];

		console.log('🛠️ allowedActions', allowedActions);
		console.log('🛠️ actionsList', actionsList);

		if (!Array.isArray(actionsList)) {
			return {
				success: false,
				error: 'Lista de ações não encontrada no configChain',
				actionName
			};
		}

		if (!Array.isArray(allowedActions)) {
			return {
				success: false,
				error: 'Erro ao carregar lista de ações permitidas.',
				actionName
			};
		}

		const matchedAction = actionsList.find(
			(action: any) => action?.name?.toLowerCase() === actionName?.toLowerCase()
		);

		console.log('🛠️ matchedAction', matchedAction);

		if (!matchedAction) {
			return {
				success: false,
				error: `Ação '${actionName}' não encontrada na lista de ações.`,
				actionName
			};
		}

		const isAllowed = allowedActions.some(
			(action: any) => action?.actionName?.toLowerCase() === actionName?.toLowerCase()
		);

		console.log('🛠️ isAllowed', isAllowed);

		if (!isAllowed) {
			return {
				success: false,
				error: `A ação '${actionName}' não é permitida para o usuário atual neste contexto. Verifique suas permissões ou entre em contato com o administrador.`,
				actionName
			};
		}

		const finalBody = {
			actionName: matchedAction.name,
			actionId: matchedAction.id,
			screenId,
			vars: variables
		};

		console.log('📤 Enviando chamada run_actions para backend:', finalBody);

		try {
			// montar curl
			// const endpoint = `${baseUrl}/businessAiShortcut/action/run`;
			// const headers = {
			//   Accept: 'application/json',
			//   Authorization: authToken,
			//   'Content-Type': 'application/json'
			// };
			// const body = JSON.stringify(finalBody, null, 2); // formatado para legibilidade

			// const curlCommand = [
			//   `curl -X POST "${endpoint}"`,
			//   `-H "Accept: application/json"`,
			//   `-H "Authorization: ${authToken}"`,
			//   `-H "Content-Type: application/json"`,
			//   `--data '${body}'`
			// ].join(' \\\n  ');

			// console.log('🐚 Requisição CURL equivalente:\n', curlCommand);

			const response = await fetch(`${baseUrl}/businessAiShortcut/action/run`, {
				method: 'POST',
				headers: {
					Accept: 'application/json',
					Authorization: authToken,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(finalBody)
			});

			if (!response.ok) {
				const errorText = await response.text();
				let errorBody;
				try {
					errorBody = JSON.parse(errorText);
				} catch {
					errorBody = errorText;
				}

				return {
					success: false,
					statusCode: response.status,
					error: errorBody || 'Erro ao executar ação',
					actionName: matchedAction.actionName,
					actionId: matchedAction.actionId
				};
			}

			const data = await response.json();

			return {
				success: true,
				data,
				actionName: matchedAction.actionName,
				actionId: matchedAction.actionId
			};
		} catch (error) {
			console.error('❌ Erro de rede ao executar run_actions:', error);

			return {
				success: false,
				error: error instanceof Error ? error.message : 'Erro desconhecido',
				actionName: matchedAction.actionName,
				actionId: matchedAction.actionId
			};
		}
	};

	// Processar cada function call
	const functionResponses: any[] = [];

	for (const functionCall of functionCalls) {
		if (functionCall.name === 'run_sql') {
			const args = functionCall.args as { query: string; jdbcConfigId?: number };
			const query = args?.query;
			const jdbcConfigId = args?.jdbcConfigId;

			if (query) {
				try {
					const result = await executeSQLFunction(query, jdbcConfigId);

					// Log da execução da ferramenta (não precisa enviar via stream pois será processado pelo Gemini)
					console.log('🔧 Tool execution result:', {
						functionName: functionCall.name,
						query,
						jdbcConfigId: jdbcConfigId || payload.jdbcConfigId || 1,
						success: result.success
					});

					functionResponses.push({
						functionResponse: {
							name: 'run_sql',
							response: result
						}
					});
				} catch (error) {
					console.error('Erro ao executar SQL:', error);

					// Log do erro da ferramenta
					console.error('🔧 Tool execution error:', {
						functionName: functionCall.name,
						query,
						jdbcConfigId: jdbcConfigId || payload.jdbcConfigId || 1,
						error: error instanceof Error ? error.message : 'Erro desconhecido'
					});

					functionResponses.push({
						functionResponse: {
							name: 'run_sql',
							response: {
								success: false,
								error: error instanceof Error ? error.message : 'Erro desconhecido'
							}
						}
					});
				}
			}
		}
		if (functionCall.name === 'run_actions') {
			const args = functionCall.args as {
				actionName: string;
				variables?: Record<string, string>;
			};
			const actionName = args?.actionName;
			const variables = args?.variables || {};

			console.log(`⚙️ Executando run_actions: ${actionName}`);
			console.log('📦 Variáveis:', variables);

			try {
				const result = await executeRunAction(actionName, variables);

				functionResponses.push({
					functionResponse: {
						name: 'run_actions',
						response: result
					}
				});
			} catch (error) {
				console.error('❌ Erro ao executar run_actions:', error);

				functionResponses.push({
					functionResponse: {
						name: 'run_actions',
						response: {
							success: false,
							error: error instanceof Error ? error.message : 'Unknown error'
						}
					}
				});
			}
		}
	}

	// Se há respostas de function calls, fazer nova chamada ao Gemini
	if (functionResponses.length > 0) {
		const updatedContents = [
			...cleanedContents,
			{
				role: 'model',
				parts: modelParts
			},
			{
				role: 'user',
				parts: functionResponses
			}
		];

		console.log('🔄 Fazendo nova chamada ao Gemini com respostas das ferramentas');

		const followUpResponse = await ai.models.generateContentStream({
			model: payload.model,
			contents: updatedContents,
			config: toolConfig
		});

		// Processar resposta do follow-up - enviar response completo como o useGeminiAI.ts fazia
		for await (const response of followUpResponse) {
			// Capturar grounding metadata se disponível (LOG TODOS OS CHUNKS)
			if (response.candidates?.[0]?.groundingMetadata) {
				const newMetadata = response.candidates[0].groundingMetadata;

				console.log('🔍 GROUNDING DEBUG - CHUNK FUNCTION CALL RECEBIDO:', {
					hasGroundingSupports: !!newMetadata.groundingSupports,
					hasSearchEntryPoint: !!newMetadata.searchEntryPoint,
					hasWebSearchQueries: !!newMetadata.webSearchQueries,
					groundingSupportsCount: newMetadata.groundingSupports?.length || 0,
					webSearchQueriesCount: newMetadata.webSearchQueries?.length || 0,
					keys: Object.keys(newMetadata)
				});

				// SEMPRE substitui para ver todos os chunks
				functionCallGroundingMetadata = newMetadata;
				console.log('🔍 GROUNDING DEBUG - Metadata atualizado (function call)');
			}

			// Enviar a resposta completa no formato original do Gemini (como era no useGeminiAI.ts)
			sendChunk(response);

			// Processar function calls e chunks de texto para execução recursiva no server-side
			if (response.candidates?.[0]?.content?.parts) {
				for (const part of response.candidates[0].content.parts) {
					if (part.functionCall) {
						console.log('🔄 Detectados mais function calls, processando recursivamente...');
						const recursiveMetadata = await processFunctionCalls(
							[part.functionCall],
							[part],
							ai,
							payload,
							updatedContents,
							toolConfig,
							event,
							sendChunk,
							addChunkToProcess
						);

						// Se chamada recursiva retornou grounding metadata mais completo, usar ele
						if (
							recursiveMetadata &&
							(!functionCallGroundingMetadata || recursiveMetadata.groundingSupports)
						) {
							functionCallGroundingMetadata = recursiveMetadata;
							console.log('🔍 GROUNDING DEBUG - Metadata recursivo usado');
						}
					} else if (part.text) {
						// Adicionar chunk de texto ao processamento throttled (igual ao useGeminiAI.ts)
						addChunkToProcess(part.text);
					}
				}
			}
		}
	}

	// Retornar grounding metadata capturado durante function calls
	return functionCallGroundingMetadata;
}
