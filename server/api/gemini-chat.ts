/* eslint-disable no-console */
import { GoogleGenAI, Type } from '@google/genai';

export const runtime = 'edge';

// Função simples para limpar parts vazias do payload
const cleanContents = (contents: any[]) => {
	console.log('🧹 SERVER CLEAN CONTENTS - Input:', JSON.stringify(contents, null, 2));

	const cleaned = contents.map(content => ({
		...content,
		parts: content.parts?.filter((part: any) => {
			// Verifica se o part existe e não é um objeto vazio
			const isValid = part &&
				typeof part === 'object' &&
				Object.keys(part).length > 0 &&
				// Verifica se tem pelo menos uma propriedade com valor não vazio
				Object.values(part).some(value =>
					value !== null &&
					value !== undefined &&
					value !== '' &&
					(typeof value !== 'object' || Object.keys(value as any).length > 0)
				);

			if (!isValid) {
				console.log('🧹 SERVER CLEAN CONTENTS - Removing empty part:', JSON.stringify(part));
			}

			return isValid;
		}) || []
	})).filter(content => {
		const hasValidParts = content.parts.length > 0;
		if (!hasValidParts) {
			console.log('🧹 SERVER CLEAN CONTENTS - Removing content with no valid parts:', JSON.stringify(content));
		}
		return hasValidParts;
	});

	console.log('🧹 SERVER CLEAN CONTENTS - Output:', JSON.stringify(cleaned, null, 2));
	return cleaned;
};

export default defineLazyEventHandler(() => {
	return defineEventHandler(async (event: any) => {
		// Set headers for Server-Sent Events
		setHeader(event, 'Content-Type', 'text/plain; charset=utf-8');
		setHeader(event, 'Cache-Control', 'no-cache');
		setHeader(event, 'Connection', 'keep-alive');
		setHeader(event, 'Access-Control-Allow-Origin', '*');
		setHeader(event, 'Access-Control-Allow-Headers', 'Cache-Control');
		const {
			systemInstruction,
			userPrompt,
			customInstructions,
			model,
			googleSearchGrounding = false,
			codeExecutionEnabled = false,
			uploadedFiles,
			apiKey,
			authToken,
			baseUrl
		} = await readBody(event);

		if (!apiKey) {
			throw createError({
				statusCode: 401,
				statusMessage: 'API key is required'
			});
		}

		const ai = new GoogleGenAI({
			apiKey
		});

		// Helper function to convert file to Gemini-compatible format
		function fileToGenerativePart(
			file: any
		): { inlineData: { data: string; mimeType: string } } {
			return {
				inlineData: {
					data: file.data,
					mimeType: file.mimeType
				}
			};
		}

		// Implementação da função run_sql
		const runSql = async (params: {
			query: string;
			jdbcConfigId?: number;
		}) => {
			const {
				query,
				jdbcConfigId = 1
			} = params;

			try {
				// Make the API request
				const response = await fetch(`${baseUrl || 'https://api0.mitraecp.com:1005'}/iaShortcuts/query`, {
					method: 'POST',
					headers: {
						Accept: 'application/json, text/plain, */*',
						Authorization: authToken || '',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ query, jdbcConfigId })
				});

				if (!response.ok) {
					let errorBody = null;
					let errorMessage = `HTTP ${response.status} - ${response.statusText}`;

					try {
						const errorText = await response.text();
						if (errorText) {
							try {
								errorBody = JSON.parse(errorText);
								errorMessage = errorBody.message || errorBody.error || errorMessage;
							} catch {
								errorMessage = errorText;
							}
						}
					} catch (parseError) {
						console.warn('Could not parse error response:', parseError);
					}

					return {
						success: false,
						error: errorMessage,
						statusCode: response.status,
						statusText: response.statusText,
						errorBody,
						query,
						jdbcConfigId
					};
				}

				const data = await response.json();
				return {
					success: true,
					data,
					query,
					jdbcConfigId
				};
			} catch (error) {
				console.error('Error executing SQL query:', error);

				let errorMessage = 'Unknown error';
				let errorDetails = null;

				if (error instanceof Error) {
					errorMessage = error.message;
					errorDetails = {
						name: error.name,
						stack: error.stack
					};
				} else if (typeof error === 'string') {
					errorMessage = error;
				} else {
					errorDetails = error;
				}

				return {
					success: false,
					error: errorMessage,
					errorDetails,
					query,
					jdbcConfigId,
					errorType: 'network_or_parsing_error'
				};
			}
		};

		// Helper function to send streaming data
		const sendStreamData = (type: string, data: any) => {
			const streamData = JSON.stringify({ type, data });
			event.node.res.write(`data: ${streamData}\n\n`);
		};

		/**
		 * Valida se a resposta do Gemini contém apenas conteúdo de pensamento
		 * Lança erro para forçar retry quando necessário
		 * NÃO considera erro quando há functionCall (é esperado não ter answer ainda)
		 */
		const validateGeminiResponse = (thoughts: string, answer: string, hasFunctionCalls: boolean = false, context: string = '') => {
			// Se há function calls, é esperado não ter answer ainda (Gemini está esperando resultado da função)
			if (hasFunctionCalls) {
				console.log(`🔧 SERVER GEMINI API${context ? ` (${context})` : ''} - Has function calls, skipping thought-only validation`);
				return;
			}

			// Só considera erro se não há function calls E só tem pensamento
			if (answer.trim() === '' && thoughts.trim() !== '') {
				const contextSuffix = context ? ` (${context})` : '';
				console.warn(`🚨 SERVER GEMINI API${contextSuffix} - Detected STOP with only thought content, triggering retry`);

				const thoughtOnlyError = new Error(`Gemini returned finishReason STOP but only provided thought content without actual answer${contextSuffix}`);
				(thoughtOnlyError as any).isGeminiError = true;
				(thoughtOnlyError as any).geminiErrorSource = 'server/api/gemini-chat.ts';
				(thoughtOnlyError as any).errorType = 'thought_only_response';
				(thoughtOnlyError as any).context = context;
				(thoughtOnlyError as any).geminiThoughts = thoughts; // Adiciona os pensamentos ao erro

				throw thoughtOnlyError;
			}

			// Verifica se a resposta está vazia (sem pensamento e sem resposta)
			if (answer.trim() === '' && thoughts.trim() === '') {
				const contextSuffix = context ? ` (${context})` : '';
				console.warn(`🚨 SERVER GEMINI API${contextSuffix} - Detected empty response, triggering retry`);

				const emptyResponseError = new Error(`Gemini returned empty response with no content${contextSuffix}`);
				(emptyResponseError as any).isGeminiError = true;
				(emptyResponseError as any).geminiErrorSource = 'server/api/gemini-chat.ts';
				(emptyResponseError as any).errorType = 'empty_response';
				(emptyResponseError as any).context = context;

				throw emptyResponseError;
			}
		};

		try {
			// Handle file upload for multimodal content
			const enhancedUserPrompt = [...userPrompt];

			if (uploadedFiles && uploadedFiles.length > 0) {
				// Convert all files to base64 for inline data
				const fileDataArray = await Promise.all(
					uploadedFiles.map((file: any) => fileToGenerativePart(file))
				);

				// Enhance the last user message with all files
				const lastUserMessage = enhancedUserPrompt[enhancedUserPrompt.length - 1];
				if (lastUserMessage && lastUserMessage.role === 'user') {
					(lastUserMessage.parts as any[]) = [...lastUserMessage.parts, ...fileDataArray];
				}
			}

			const contents = [
				{ role: 'model', parts: [{ text: customInstructions }] },
				...enhancedUserPrompt
			];

			// Build tools array based on enabled features
			const tools: any[] = [];

			// Add Google Search grounding if enabled
			if (googleSearchGrounding) {
				tools.push({
					googleSearch: {}
				});
				console.log('🔍 Google Search grounding enabled');
			}

			// Add Code Execution if enabled (compatible with Google Search)
			if (codeExecutionEnabled) {
				tools.push({
					codeExecution: {}
				});
				console.log('🐍 Code Execution enabled');
			}

			// Add SQL function calling only if neither Google Search nor Code Execution is enabled
			if (!googleSearchGrounding && !codeExecutionEnabled) {
				tools.push({
					functionDeclarations: [
						{
							name: 'run_sql',
							description:
								'Execute a SQL query and return the results to know if the query is valid.',
							parameters: {
								type: Type.OBJECT,
								properties: {
									query: {
										type: Type.STRING,
										description: 'The SQL query to execute, e.g. SELECT * FROM users WHERE id = 1'
									},
									jdbcConfigId: {
										type: Type.NUMBER,
										description: 'The JDBC config ID to use for the query'
									}
								},
								required: ['query', 'jdbcConfigId']
							}
						}
					]
				});
				console.log('🗄️ SQL function calling enabled');
			}

			const toolConfig = {
				systemInstruction,
				temperature: 0.01,
				tools,
				thinkingConfig: {
					includeThoughts: true
				}
			};

			// Primeira chamada com stream para capturar thinking e detectar function calls
			const initialResponse = await ai.models.generateContentStream({
				model,
				contents: cleanContents(contents),
				config: toolConfig
			});

			const functionCalls: any[] = [];
			let initialThoughts = '';
			let initialAnswer = '';
			const modelParts: any[] = [];
			let groundingMetadata = null;

			// Processa o stream inicial para capturar thinking e function calls
			for await (const chunk of initialResponse) {
				if (!chunk?.candidates?.[0]?.content?.parts) {
					console.warn('🚫 SERVER - Invalid chunk structure:', chunk);
					continue;
				}

				// Verifica se as parts não são vazias
				const chunkParts = chunk.candidates[0].content.parts;
				if (!chunkParts || (Array.isArray(chunkParts) && chunkParts.length === 0)) {
					console.warn('🚫 SERVER - Empty parts in chunk:', chunk);
					continue;
				}

				// Capture grounding metadata if available
				if (
					chunk.candidates &&
					chunk.candidates[0] &&
					chunk.candidates[0].groundingMetadata
				) {
					groundingMetadata = chunk.candidates[0].groundingMetadata;
					console.log(
						'🔍 SERVER GEMINI API - Initial grounding metadata captured:',
						groundingMetadata
					);
				}

				const parts = Array.isArray(chunk.candidates[0].content.parts)
					? chunk.candidates[0].content.parts
					: [chunk.candidates[0].content.parts];

				for (const part of parts) {
					// Verifica se a parte não é vazia antes de processar
					if (!part || typeof part !== 'object' || Object.keys(part).length === 0) {
						console.warn('🚫 SERVER - Skipping empty part:', part);
						continue;
					}

					// Verifica se tem pelo menos uma propriedade com valor válido
					const hasValidContent = Object.values(part).some(value =>
						value !== null &&
						value !== undefined &&
						value !== '' &&
						(typeof value !== 'object' || Object.keys(value as any).length > 0)
					);

					if (!hasValidContent) {
						console.warn('🚫 SERVER - Skipping part with no valid content:', part);
						continue;
					}

					console.log('🔍 SERVER - Processing part:', {
						hasThought: !!part.thought,
						hasText: !!part.text,
						hasFunctionCall: !!part.functionCall,
						hasExecutableCode: !!part.executableCode,
						hasCodeExecutionResult: !!part.codeExecutionResult,
						googleSearchGrounding,
						codeExecutionEnabled
					});

					// Captura function calls
					if (part.functionCall) {
						functionCalls.push(part.functionCall);
						modelParts.push(part);
					} else if (part.executableCode) {
						// Process generated code from Code Execution
						console.log('🐍 SERVER - Code generated:', part.executableCode.code);
						sendStreamData('code-generated', {
							code: part.executableCode.code,
							language: part.executableCode.language || 'python',
							timestamp: new Date().toISOString()
						});
						modelParts.push(part);
					} else if (part.codeExecutionResult) {
						// Process code execution results
						console.log('📊 SERVER - Code execution result:', part.codeExecutionResult.output);
						sendStreamData('code-executed', {
							output: part.codeExecutionResult.output,
							outcome: part.codeExecutionResult.outcome,
							timestamp: new Date().toISOString()
						});
						modelParts.push(part);
					} else if (part.thought && part.text) {
						// Processa thinking em tempo real
						if (!initialThoughts) {
							console.log('🧠 SERVER - Thoughts summary (with thought flag):');
						}
						console.log('🧠 SERVER -', part.text);
						initialThoughts = initialThoughts + part.text;
						sendStreamData('thought', part.text);
						modelParts.push(part);
					} else if (part.text) {
						// FIXED: When Google Search grounding is enabled, treat all text as answer
						console.log('💬 SERVER - Answer part:', part.text);
						initialAnswer = initialAnswer + part.text;
						sendStreamData('answer', part.text);
						modelParts.push(part);
					}
				}
			}

			// Adiciona a resposta inicial do modelo ao histórico se houver partes válidas
			if (modelParts.length > 0) {
				// Filtra partes vazias antes de adicionar ao contents
				const validParts = modelParts.filter(part =>
					part &&
					typeof part === 'object' &&
					Object.keys(part).length > 0 &&
					Object.values(part).some(value =>
						value !== null &&
						value !== undefined &&
						value !== '' &&
						(typeof value !== 'object' || Object.keys(value as any).length > 0)
					)
				);

				if (validParts.length > 0) {
					console.log('🔄 SERVER - Adding initial model parts to contents:', validParts.length, 'valid parts');
					contents.push({
						role: 'model',
						parts: validParts as { text: string }[]
					});
				} else {
					console.log('🚫 SERVER - Skipping empty model parts for initial response');
				}
			}

			// Validação inicial: Verifica se a primeira resposta tem conteúdo válido
			// Só valida se não há function calls pendentes (se há, é esperado não ter answer ainda)
			const hasPendingFunctionCalls = functionCalls.length > 0;
			validateGeminiResponse(initialThoughts, initialAnswer, hasPendingFunctionCalls, 'initial_response');

			// Loop infinito para processar function calls até que não haja mais
			let currentFunctionCalls = functionCalls;
			let iterationCount = 0;
			const maxIterations = 10; // Limite de segurança para evitar loops infinitos

			while (
				currentFunctionCalls &&
				currentFunctionCalls.length > 0 &&
				iterationCount < maxIterations
			) {
				iterationCount++;
				console.log(
					`SERVER - Function calls iteration ${iterationCount}:`,
					currentFunctionCalls
				);

				// Executa cada function call da iteração atual
				for (const functionCall of currentFunctionCalls) {
					if (functionCall.name === 'run_sql') {
						try {
							const args = functionCall.args as { query: string; jdbcConfigId: number };
							const sqlResult = await runSql({
								query: args?.query || '',
								jdbcConfigId: args?.jdbcConfigId || 1
							});

							// Stream tool execution result
							sendStreamData('tool-run_sql', {
								functionName: functionCall.name,
								query: args?.query || '',
								jdbcConfigId: args?.jdbcConfigId || 1,
								result: sqlResult,
								timestamp: new Date().toISOString(),
								success: sqlResult.success
							});

							// Adiciona o resultado da function call ao histórico
							contents.push({
								role: 'user',
								parts: [
									{
										text: `Function ${functionCall.name} result: ${JSON.stringify(sqlResult)}`
									}
								]
							});

							console.log('SERVER - SQL function executed:', args?.query, 'Result:', sqlResult);
						} catch (error) {
							console.error('SERVER - Error executing SQL function:', error);

							// Stream tool execution error
							const args = functionCall.args as { query: string; jdbcConfigId: number };
							sendStreamData('tool-run_sql', {
								functionName: functionCall.name,
								query: args?.query || '',
								jdbcConfigId: args?.jdbcConfigId || 1,
								error,
								timestamp: new Date().toISOString(),
								success: false
							});

							// Cria uma mensagem de erro detalhada para o modelo
							let errorMessage = `Function ${functionCall.name} failed to execute.`;

							if (error && typeof error === 'object') {
								const errorObj = error as any;

								if (errorObj.statusCode) {
									errorMessage += ` HTTP ${errorObj.statusCode}`;
									if (errorObj.statusText) {
										errorMessage += ` - ${errorObj.statusText}`;
									}
								}

								if (errorObj.error) {
									errorMessage += `\nError: ${errorObj.error}`;
								}

								if (errorObj.errorBody) {
									errorMessage += `\nDetails: ${JSON.stringify(errorObj.errorBody, null, 2)}`;
								}

								if (errorObj.errorType) {
									errorMessage += `\nType: ${errorObj.errorType}`;
								}

								if (errorObj.query) {
									errorMessage += `\nQuery: ${errorObj.query}`;
								}
								if (errorObj.jdbcConfigId) {
									errorMessage += `\nJDBC Config ID: ${errorObj.jdbcConfigId}`;
								}
							} else if (error instanceof Error) {
								errorMessage += `\nError: ${error.message}`;
								if (error.stack) {
									errorMessage += `\nStack: ${error.stack}`;
								}
							} else {
								errorMessage += `\nError: ${String(error)}`;
							}

							// Adiciona o erro detalhado como resposta da function
							contents.push({
								role: 'user',
								parts: [
									{
										text: errorMessage
									}
								]
							});
						}
					}
				}

				// Faz nova chamada para verificar se há mais function calls
				const nextResponse = await ai.models.generateContentStream({
					model,
					contents: cleanContents(contents),
					config: toolConfig
				});

				// Processa a resposta para detectar novos function calls
				const nextResult = await processStreamResponseWithFunctionCalls(nextResponse);

				// Adiciona os novos thoughts e answer
				initialThoughts = initialThoughts + nextResult.thoughts;
				initialAnswer = initialAnswer + nextResult.answer;

				// Update grounding metadata if available from subsequent calls
				if (nextResult.groundingMetadata) {
					groundingMetadata = nextResult.groundingMetadata;
				}

				// Adiciona as partes do modelo ao histórico se houver partes válidas
				if (nextResult.modelParts.length > 0) {
					// Filtra partes vazias antes de adicionar ao contents
					const validParts = nextResult.modelParts.filter(part =>
						part &&
						typeof part === 'object' &&
						Object.keys(part).length > 0 &&
						Object.values(part).some(value =>
							value !== null &&
							value !== undefined &&
							value !== '' &&
							(typeof value !== 'object' || Object.keys(value as any).length > 0)
						)
					);

					if (validParts.length > 0) {
						console.log('🔄 SERVER - Adding subsequent model parts to contents:', validParts.length, 'valid parts');
						contents.push({
							role: 'model',
							parts: validParts as { text: string }[]
						});
					} else {
						console.log('🚫 SERVER - Skipping empty model parts for subsequent response');
					}
				}

				// Atualiza para a próxima iteração
				currentFunctionCalls = nextResult.functionCalls;
			}

			// Se não houve function calls na primeira chamada, usa os resultados iniciais
			if (functionCalls.length === 0) {
				console.log(
					'🔍 SERVER - No function calls - preserving grounding metadata:',
					groundingMetadata
				);
			}

			console.log('🔍 SERVER GEMINI API - Final return:', {
				thoughts: initialThoughts.length,
				answer: initialAnswer.length,
				groundingMetadata,
				hasGroundingMetadata: !!groundingMetadata
			});

			// Send final result and close stream
			sendStreamData('final', {
				thoughts: initialThoughts,
				answer: initialAnswer,
				groundingMetadata
			});

			// End the stream
			event.node.res.end();

		} catch (error) {
			console.error('SERVER - Error generating Gemini response:', error);
			throw createError({
				statusCode: 500,
				statusMessage: 'Error generating Gemini response'
			});
		}



		async function processStreamResponseWithFunctionCalls(response: any) {
			let thoughts = '';
			let answer = '';
			const functionCalls: any[] = [];
			const modelParts: any[] = [];
			let groundingMetadata = null;

			for await (const chunk of response) {
				// Ensure chunk and its properties exist before accessing
				if (!chunk?.candidates?.[0]?.content?.parts) {
					console.warn('🚫 SERVER - Invalid subsequent chunk structure:', chunk);
					continue;
				}

				// Capture grounding metadata if available
				if (
					chunk.candidates &&
					chunk.candidates[0] &&
					chunk.candidates[0].groundingMetadata
				) {
					groundingMetadata = chunk.candidates[0].groundingMetadata;
					console.log('🔍 SERVER - Subsequent grounding metadata captured:', groundingMetadata);
				}

				const parts = Array.isArray(chunk.candidates[0].content.parts)
					? chunk.candidates[0].content.parts
					: [chunk.candidates[0].content.parts];

				for (const part of parts) {
					// Verifica se a parte não é vazia antes de processar
					if (!part || typeof part !== 'object' || Object.keys(part).length === 0) {
						console.warn('🚫 SERVER - Skipping empty subsequent part:', part);
						continue;
					}

					// Verifica se tem pelo menos uma propriedade com valor válido
					const hasValidContent = Object.values(part).some(value =>
						value !== null &&
						value !== undefined &&
						value !== '' &&
						(typeof value !== 'object' || Object.keys(value as any).length > 0)
					);

					if (!hasValidContent) {
						console.warn('🚫 SERVER - Skipping subsequent part with no valid content:', part);
						continue;
					}

					console.log('🔍 SERVER - Processing subsequent part:', {
						hasThought: !!part.thought,
						hasText: !!part.text,
						hasFunctionCall: !!part.functionCall,
						hasExecutableCode: !!part.executableCode,
						hasCodeExecutionResult: !!part.codeExecutionResult
					});

					// Captura function calls
					if (part.functionCall) {
						functionCalls.push(part.functionCall);
						modelParts.push(part);
					} else if (part.executableCode) {
						// Process subsequent generated code from Code Execution
						console.log('🐍 SERVER - Subsequent code generated:', part.executableCode.code);
						sendStreamData('code-generated', {
							code: part.executableCode.code,
							language: part.executableCode.language || 'python',
							timestamp: new Date().toISOString()
						});
						modelParts.push(part);
					} else if (part.codeExecutionResult) {
						// Process subsequent code execution results
						console.log(
							'📊 SERVER - Subsequent code execution result:',
							part.codeExecutionResult.output
						);
						sendStreamData('code-executed', {
							output: part.codeExecutionResult.output,
							outcome: part.codeExecutionResult.outcome,
							timestamp: new Date().toISOString()
						});
						modelParts.push(part);
					} else if (part.thought && part.text) {
						// Processa thinking em tempo real
						if (!thoughts) {
							console.log('🧠 SERVER - Subsequent thoughts summary (with thought flag):');
						}
						console.log('🧠 SERVER -', part.text);
						thoughts = thoughts + part.text;
						sendStreamData('thought', part.text);
						modelParts.push(part);
					} else if (part.text) {
						// Processa resposta
						if (!answer) {
							console.log('💬 SERVER - Subsequent answer:');
						}
						console.log('💬 SERVER -', part.text);
						answer = answer + part.text;
						sendStreamData('answer', part.text);
						modelParts.push(part);
					}
				}
			}

			// Validação: Verifica se retornou finishReason STOP mas só tem conteúdo de pensamento
			// Este método sempre processa function calls, então verifica se há calls detectados
			const hasFunctionCallsDetected = functionCalls.length > 0;
			validateGeminiResponse(thoughts, answer, hasFunctionCallsDetected, 'processStreamResponseWithFunctionCalls');

			// console.log('🔍 processStreamResponseWithFunctionCalls - Final return:', { thoughts: thoughts.length, answer: answer.length, groundingMetadata });
			return { thoughts, answer, functionCalls, modelParts, groundingMetadata };
		}
	});
});
