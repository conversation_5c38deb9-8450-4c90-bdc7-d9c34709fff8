<template>
	<div class="flex flex-col group py-2">
		<div
			class="database-add-item py-2 px-4 group-first:hover:bg-grey-25 cursor-pointer flex items-center"
			@click="emit('new-content')"
		>
			<HugeiconsIcon
				:icon="BubbleChatIcon"
				color="var(--grey-500)"
				:size="16"
				class="mr-2"
			/>
			{{ $t('AI.new_chat') }}
		</div>
		<div
			class="database-add-item py-2 px-4 group-last:hover:bg-grey-25 cursor-pointer flex items-center"
			@click="emit('new-dashboard')"
		>
			<div class="flex">
				<HugeiconsIcon
					:icon="DashboardSquare02Icon"
					color="var(--grey-500)"
					:size="16"
					class="mr-2"
				/>
			</div>
			<div>Dashboard</div>
		</div>
		<!-- <div
			class="database-add-item py-2 px-4 group-last:hover:bg-grey-25 cursor-pointer flex items-center truncate"
			@click="emit('new-mobile')"
		>
			<HugeiconsIcon
				:icon="SmartPhone01Icon"
				color="var(--grey-500)"
				:size="16"
				class="mr-2"
			/>
			{{ $t('PROJECT.mobile_screen') }}
		</div> -->
	</div>
</template>

<script lang="ts" setup>
	import { HugeiconsIcon } from '@hugeicons/vue';
	import {
		DashboardSquare02Icon,
		// SmartPhone01Icon,
		BubbleChatIcon
	} from '@hugeicons-pro/core-stroke-rounded';

	const emit = defineEmits([
		'new-content',
		'new-mobile',
		'new-dashboard',
		'cancel'
	]);
</script>

<style lang="postcss" scoped>
	.custom-color-filter img {
		filter: brightness(1.7);
	}
</style>
