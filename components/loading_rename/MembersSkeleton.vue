<template>
	<div class="w-full animate-pulse">
		<!-- Header -->
		<div class="mb-4 flex items-center justify-between">
			<div class="h-8 w-48 rounded bg-grey-200"></div>
			<div class="flex items-center space-x-2">
				<div class="h-10 w-64 rounded bg-grey-200"></div>
				<div class="h-10 w-32 rounded bg-grey-200"></div>
			</div>
		</div>

		<!-- Table -->
		<div class="overflow-hidden rounded-lg border light-border-all">
			<div class="border-b light-border-all bg-grey-50 p-4">
				<div class="grid grid-cols-4 gap-4">
					<div class="h-4 w-24 rounded bg-grey-300"></div>
					<div class="h-4 w-24 rounded bg-grey-300"></div>
					<div class="h-4 w-32 rounded bg-grey-300"></div>
					<div class="h-4 w-24 rounded bg-grey-300"></div>
				</div>
			</div>
			<div>
				<!-- Skeleton Rows -->
				<div
					v-for="i in 4"
					:key="i"
					class="grid grid-cols-4 items-center gap-4 border-b light-border-all p-4"
				>
					<!-- User Info -->
					<div class="flex items-center space-x-3">
						<div class="h-10 w-10 rounded-full bg-grey-300"></div>
						<div class="flex-1 space-y-2">
							<div class="h-4 w-3/4 rounded bg-grey-300"></div>
							<div class="h-3 w-1/2 rounded bg-grey-300"></div>
						</div>
					</div>
					<!-- Permission -->
					<div class="h-8 w-32 rounded bg-grey-300"></div>
					<!-- Access -->
					<div class="h-6 w-12 rounded-full bg-grey-300"></div>
					<!-- Actions -->
					<div class="flex items-center justify-end space-x-2">
						<div class="h-4 w-20 rounded bg-grey-300"></div>
						<div class="h-5 w-5 rounded bg-grey-300"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	// Componente de skeleton, sem lógica necessária.
</script>
