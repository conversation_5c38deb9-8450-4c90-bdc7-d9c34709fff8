<template>
	<div
		class="table-add-menu flex w-[1000px] h-[80vh] flex-col border border-grey-200 rounded-lg shadow-md"
	>
		<div
			class="flex items-center justify-between h-[50px] px-4 border-b border-grey-200 flex-shrink-0"
		>
			<div class="title font-medium text-lg">
				{{ $t('TABLE_ADD_MENU.default_title') }}
			</div>
			<svg-icon
				class="cursor-pointer text-grey-500 hover:text-grey-700"
				size="18"
				type="mdi"
				:path="mdiClose"
				@click="closeModal()"
			></svg-icon>
		</div>

		<div class="flex flex-col overflow-y-auto flex-grow borders">
			<div class="flex flex-col mb-[10px]">
				<div class="text mb-[6px]">
					{{ $t('GLOBAL.name') }}
				</div>
				<a-input
					ref="input"
					v-model:value="tableName"
					v-focus
					:placeholder="$t('DATABASE.add_table_placeholder')"
					data-cy="input-name"
					class="mb-1 w-full"
					:maxlength="maxCharacters"
				/>
			</div>
			<div class="text mb-[6px]">
				{{ $t('CONNECTION.columns') }}
			</div>

			<div
				class="grid grid-cols-[24px_1fr_1fr_1fr_50px_20px_30px] gap-x-4 mb-2 px-2 items-center"
			>
				<div></div>
				<div class="text-xs text-grey-500 font-medium">{{ $t('GLOBAL.name') }}</div>
				<div class="text-xs text-grey-500 font-medium">
					{{ $t('TABLE_ADD_MENU.column_headers.type') }}
				</div>
				<div class="text-xs text-grey-500 font-medium flex items-center">
					{{ $t('TABLE_ADD_MENU.column_headers.default_value') }}
					<a-tooltip
						placement="top"
						overlay-class-name="base-tooltip dark-tooltip "
					>
						<template #title>
							<div class="flex flex-col gap-1 text-left">
								<span class="base-tooltip-text">
									{{ $t('CREATE_COLUMN_MODAL.constraints.primary.description') }}
								</span>
							</div>
						</template>
						<HugeiconsIcon
							:icon="HelpCircleIcon"
							color="var(--grey-500)"
							class="ml-1"
							:size="15"
						/>
					</a-tooltip>
				</div>
				<div class="text-xs text-grey-500 font-medium text-center">
					{{ $t('TABLE_ADD_MENU.column_headers.primary') }}
				</div>
				<div></div>
				<div></div>
			</div>

			<div class="flex flex-col space-y-1">
				<DroppableWrapper
					class="flex flex-col w-full"
					:on-drop="(e: DragEvent) => onDropColumn(e, true)"
					:on-drag-over="(e: DragEvent) => onDragOverColumn(e, true)"
					:on-drag-leave="onDragLeaveColumn"
				>
					<template v-if="primaryColumns.length === 0 && isDragging && dragOverIsPrimary">
						<div class="h-0.5 bg-violet-600 rounded transition-all my-1"></div>
					</template>
					<template
						v-for="(column, index) in primaryColumns"
						:key="column.id"
					>
						<div
							v-if="dragOverIsPrimary && dragOverIndex === index && isDragging"
							class="h-0.5 bg-violet-600 rounded transition-all mb-1"
						></div>
						<DraggableWrapper
							:transfer-data="{ column, index }"
							:non-draggable-selector="'non-draggable'"
						>
							<div
								class="grid grid-cols-[24px_1fr_1fr_1fr_50px_20px_30px] gap-x-4 items-center p-2 rounded hover:bg-grey-100 group bg-[var(--grey-25)]"
							>
								<svg-icon
									type="mdi"
									:path="mdiDragVertical"
									size="20"
									class="cursor-grab text-grey-400 hover:text-grey-600"
								/>
								<a-input
									v-model:value="column.name"
									:placeholder="$t('TABLE_ADD_MENU.placeholders.column_name')"
									class="non-draggable"
								>
									<template #suffix>
										<div
											class="suffix cursor-pointer"
											:class="{
												active: isColumnForeignKey(column.name)
											}"
											@click="addForeignKeyConnection"
										>
											<HugeiconsIcon
												:icon="Link03Icon"
												color="var(--grey-700)"
												:size="17"
											/>
										</div>
									</template>
								</a-input>
								<a-select
									v-model:value="column.type"
									:placeholder="$t('TABLE_ADD_MENU.placeholders.select_type')"
									class="w-full non-draggable"
									@change="onColumnTypeChange(column)"
								>
									<a-select-option
										v-for="dataType in dataTypes"
										:key="dataType"
										:value="dataType"
										><div class="flex items-center">
											{{ dataType }}
											<a-tooltip
												v-if="dataType === 'TEXT' || dataType === 'VARCHAR'"
												placement="top"
												overlay-class-name="base-tooltip dark-tooltip"
											>
												<template #title>
													<div class="flex flex-col gap-1 text-left">
														<span class="base-tooltip-text">
															{{
																$t(
																	dataType === 'TEXT' ? 'CREATE_COLUMN_MODAL.text' : 'CREATE_COLUMN_MODAL.varchar'
																)
															}}
														</span>
													</div>
												</template>
												<HugeiconsIcon
													:icon="HelpCircleIcon"
													color="var(--grey-500)"
													class="ml-1"
													:size="15"
												/>
											</a-tooltip>
										</div>
									</a-select-option>
								</a-select>
								<a-input
									v-if="column.type !== 'DATE' && column.type !== 'BOOLEAN'"
									v-model:value="column.defaultValue"
									:type="['INT', 'FLOAT'].includes(`${column?.type}`) ? 'number' : 'text'"
									:placeholder="$t('CREATE_COLUMN_MODAL.placeholders.null')"
									class="w-full"
									:disabled="true"
									@keydown="handleKeydown($event, column)"
								/>
								<a-date-picker
									v-else-if="column.type === 'DATE'"
									v-model:value="column.defaultValue"
									:disabled="true"
									:placeholder="$t('GLOBAL.insert')"
									class="w-full"
									value-format="DD-MM-YYYY"
								/>
								<a-checkbox
									v-else-if="column.type === 'BOOLEAN'"
									v-model:checked="column.defaultValue"
									class="mx-auto"
									:disabled="true"
								/>
								<div class="flex items-center justify-center">
									<a-checkbox
										:checked="column.isPrimary"
										class="custom-checkbox non-draggable"
										@change="() => onPrimaryChange(column)"
									/>
								</div>

								<a-popover
									trigger="click"
									placement="bottom"
									overlay-class-name="database-menu"
								>
									<HugeiconsIcon
										:icon="Settings01Icon"
										color="var(--grey-700)"
										:size="17"
										class="cursor-pointer non-draggable"
									/>
									<template #content>
										<div
											class="create-column-modal flex w-[340px] min-h-[auto] max-h-[calc(100vh-80px)] flex-col border border-grey-200 rounded-lg shadow-md bg-white"
										>
											<!-- Cabeçalho do Modal -->
											<div class="flex items-center justify-between h-[50px] px-4 flex-shrink-0">
												<div class="title font-medium text-lg">
													{{ $t('CREATE_COLUMN_MODAL.extra_options') }}
												</div>
											</div>
 
											<!-- Corpo do Modal -->
											<div class="flex flex-col overflow-y-auto flex-grow p-6 space-y-5 borders">
												<div class="space-y-4">
													<!-- Única -->
													<div class="flex items-start space-x-3">
														<a-checkbox
															v-model:checked="column.isUnique"
															:disabled="column.isPrimary"
															class="custom-checkbox mt-1"
														></a-checkbox>
														<div>
															<div class="text font-medium">
																{{ $t('CREATE_COLUMN_MODAL.constraints.unique.label') }}
															</div>
															<div class="text-xs text-grey-500">
																{{ $t('CREATE_COLUMN_MODAL.constraints.unique.description') }}
															</div>
														</div>
													</div>
													<!-- Obrigatória -->
													<div class="flex items-start space-x-3">
														<a-checkbox
															:checked="!column.isNullable"
															:disabled="true"
															class="custom-checkbox non-draggable"
															@change="column.isNullable = !column.isNullable"
														/>
														<div>
															<div class="text font-medium">
																{{ $t('CREATE_COLUMN_MODAL.constraints.required.label') }}
															</div>
															<div class="text-xs text-grey-500">
																{{ $t('CREATE_COLUMN_MODAL.constraints.required.description') }}
															</div>
														</div>
													</div>
													<!-- Autoincrement -->
													<div class="flex items-start space-x-3">
														<a-checkbox
															v-model:checked="column.autoIncrement"
															:disabled="(!column.isUnique && !column.isPrimary) || column.type !== 'INT'"
															class="custom-checkbox mt-1"
														/>
														<div>
															<div class="text font-medium">Autoincrement</div>
															<div class="text-xs text-grey-500">
																{{ $t('CREATE_COLUMN_MODAL.constraints.auto_increment.description') }}
															</div>
														</div>
													</div>
												</div>
												<div class="mt-4">
													<div class="text mb-[6px]">{{ $t('CREATE_COLUMN_MODAL.field_size') }}</div>
													<a-input
														ref="inputNameRef"
														v-model:value="column.fieldSize"
														v-focus
														:disabled="column.type !== 'VARCHAR'"
														:placeholder="$t('DRIVE.size')"
														class="w-full"
														:maxlength="63"
														@keydown="handleKeydown($event, column, true)"
													/>
												</div>
											</div>
										</div>
									</template>
								</a-popover>
								<svg-icon
									class="cursor-pointer text-grey-500 non-draggable"
									size="18"
									type="mdi"
									:path="mdiClose"
									@click="removeColumn(getColumnIndex(column))"
								/>
							</div>
						</DraggableWrapper>
					</template>
					<div
						v-if="
							dragOverIsPrimary && dragOverIndex === primaryColumns.length && isDragging
						"
						class="h-0.5 bg-violet-600 rounded transition-all mt-1"
					></div>
				</DroppableWrapper>
				<DroppableWrapper
					class="flex flex-col w-full"
					:on-drop="(e: DragEvent) => onDropColumn(e, false)"
					:on-drag-over="(e: DragEvent) => onDragOverColumn(e, false)"
					:on-drag-leave="onDragLeaveColumn"
				>
					<template
						v-if="nonPrimaryColumns.length === 0 && isDragging && !dragOverIsPrimary"
					>
						<div class="h-0.5 bg-violet-600 rounded transition-all my-1"></div>
					</template>
					<template
						v-for="(column, index) in nonPrimaryColumns"
						:key="column.id"
					>
						<div
							v-if="!dragOverIsPrimary && dragOverIndex === index && isDragging"
							class="h-0.5 bg-violet-600 rounded transition-all mb-1"
						></div>
						<DraggableWrapper
							:transfer-data="{ column, index }"
							:non-draggable-selector="'.non-draggable'"
						>
							<div
								class="grid grid-cols-[24px_1fr_1fr_1fr_50px_20px_30px] gap-x-4 items-center p-2 rounded hover:bg-grey-100 group"
							>
								<svg-icon
									type="mdi"
									:path="mdiDragVertical"
									size="20"
									class="cursor-grab text-grey-400 hover:text-grey-600"
								/>
								<a-input
									v-model:value="column.name"
									:placeholder="$t('TABLE_ADD_MENU.placeholders.column_name')"
									class="non-draggable"
								>
									<template #suffix>
										<div
											class="suffix cursor-pointer"
											:class="{
												active: isColumnForeignKey(column.name)
											}"
											@click="addForeignKeyConnection"
										>
											<HugeiconsIcon
												:icon="Link03Icon"
												color="var(--grey-700)"
												:size="17"
											/>
										</div>
									</template>
								</a-input>
								<a-select
									v-model:value="column.type"
									:placeholder="$t('TABLE_ADD_MENU.placeholders.select_type')"
									class="w-full non-draggable"
									@change="onColumnTypeChange(column)"
								>
									<a-select-option
										v-for="dataType in dataTypes"
										:key="dataType"
										:value="dataType"
										><div class="flex items-center">
											{{ dataType }}
											<a-tooltip
												v-if="dataType === 'TEXT' || dataType === 'VARCHAR'"
												placement="top"
												overlay-class-name="base-tooltip dark-tooltip"
											>
												<template #title>
													<div class="flex flex-col gap-1 text-left">
														<span class="base-tooltip-text">
															{{
																$t(
																	dataType === 'TEXT' ? 'CREATE_COLUMN_MODAL.text' : 'CREATE_COLUMN_MODAL.varchar'
																)
															}}
														</span>
													</div>
												</template>
												<HugeiconsIcon
													:icon="HelpCircleIcon"
													color="var(--grey-500)"
													class="ml-1"
													:size="15"
												/>
											</a-tooltip>
										</div>
									</a-select-option>
								</a-select>
								<a-input
									v-if="column.type !== 'DATE' && column.type !== 'BOOLEAN'"
									v-model:value="column.defaultValue"
									:type="['INT', 'FLOAT'].includes(`${column?.type}`) ? 'number' : 'text'"
									:placeholder="$t('CREATE_COLUMN_MODAL.placeholders.null')"
									class="w-full"
									@keydown="handleKeydown($event, column)"
								/>
								<a-date-picker
									v-else-if="column.type === 'DATE'"
									v-model:value="column.defaultValue"
									:placeholder="$t('GLOBAL.insert')"
									class="w-full"
									value-format="DD-MM-YYYY"
								/>
								<a-checkbox
									v-else-if="column.type === 'BOOLEAN'"
									v-model:checked="column.defaultValue"
									class="mx-auto"
								/>
								<div class="flex items-center justify-center">
									<a-checkbox
										:checked="column.isPrimary"
										class="custom-checkbox non-draggable"
										@change="() => onPrimaryChange(column)"
									/>
								</div>
								<a-popover
									trigger="click"
									placement="bottom"
									overlay-class-name="database-menu"
								>
									<HugeiconsIcon
										:icon="Settings01Icon"
										color="var(--grey-700)"
										:size="17"
										class="cursor-pointer non-draggable"
									/>
									<template #content>
										<div
											class="create-column-modal flex w-[340px] min-h-[auto] max-h-[calc(100vh-80px)] flex-col border border-grey-200 rounded-lg shadow-md bg-white"
										>
											<!-- Cabeçalho do Modal -->
											<div class="flex items-center justify-between h-[50px] px-4 flex-shrink-0">
												<div class="title font-medium text-lg">
													{{ $t('CREATE_COLUMN_MODAL.extra_options') }}
												</div>
											</div>

											<!-- Corpo do Modal -->
											<div class="flex flex-col overflow-y-auto flex-grow p-6 space-y-5 borders">
												<div class="space-y-4">
													<!-- Única -->
													<div class="flex items-start space-x-3">
														<a-checkbox
															v-model:checked="column.isUnique"
															:disabled="column.isPrimary"
															class="custom-checkbox mt-1"
														></a-checkbox>
														<div>
															<div class="text font-medium">
																{{ $t('CREATE_COLUMN_MODAL.constraints.unique.label') }}
															</div>
															<div class="text-xs text-grey-500">
																{{ $t('CREATE_COLUMN_MODAL.constraints.unique.description') }}
															</div>
														</div>
													</div>
													<!-- Obrigatória -->
													<div class="flex items-start space-x-3">
														<a-checkbox
															:checked="!column.isNullable"
															class="custom-checkbox non-draggable"
															@change="column.isNullable = !column.isNullable"
														/>
														<div>
															<div class="text font-medium">
																{{ $t('CREATE_COLUMN_MODAL.constraints.required.label') }}
															</div>
															<div class="text-xs text-grey-500">
																{{ $t('CREATE_COLUMN_MODAL.constraints.required.description') }}
															</div>
														</div>
													</div>
													<!-- Autoincrement -->
													<div class="flex items-start space-x-3">
														<a-checkbox
															v-model:checked="column.autoIncrement"
															:disabled="(!column.isUnique && !column.isPrimary) || column.type !== 'INT'"
															class="custom-checkbox mt-1"
														/>
														<div>
															<div class="text font-medium">Autoincrement</div>
															<div class="text-xs text-grey-500">
																{{ $t('CREATE_COLUMN_MODAL.constraints.auto_increment.description') }}
															</div>
														</div>
													</div>
												</div>
												<div class="mt-4">
													<div class="text mb-[6px]">{{ $t('CREATE_COLUMN_MODAL.field_size') }}</div>
													<a-input
														ref="inputNameRef"
														v-model:value="column.fieldSize"
														v-focus
														:disabled="column.type !== 'VARCHAR'"
														:placeholder="$t('DRIVE.size')"
														class="w-full"
														:maxlength="63"
														@keydown="handleKeydown($event, column, true)"
													/>
												</div>
											</div>
										</div>
									</template>
								</a-popover>
								<svg-icon
									class="cursor-pointer text-grey-500 non-draggable"
									size="18"
									type="mdi"
									:path="mdiClose"
									@click="removeColumn(getColumnIndex(column))"
								/>
							</div>
						</DraggableWrapper>
					</template>
					<div
						v-if="
							!dragOverIsPrimary && dragOverIndex === nonPrimaryColumns.length && isDragging
						"
						class="h-0.5 bg-violet-600 rounded transition-all mt-1"
					></div>
				</DroppableWrapper>
			</div>

			<div class="mt-2 p-3 border border-dashed border-grey-300 rounded-lg">
				<MitraButton
					:text="$t('TABLE_ADD_MENU.add_column')"
					color="var(--grey-700)"
					class="w-fit mx-auto"
					outlined
					text-color="var(--grey-700)"
					@click="addColumn"
				/>
			</div>
			<div class="mt-4 text mb-[6px]">
				{{ $t('TABLE_ADD_MENU.foreign_key_section_title') }}
			</div>

			<div
				v-if="foreignKeys.length > 0"
				class="flex flex-col gap-y-2"
			>
				<div
					v-for="fk in foreignKeys"
					:key="fk.id"
					class="p-3 border border-solid border-grey-200 rounded-lg"
				>
					<div class="flex justify-between items-center">
						<div class="text-sm font-medium">
							{{ $t('TABLE_ADD_MENU.relation_with') }}
							<span class="font-semibold">{{ fk.table }}</span>
						</div>
						<div class="flex items-center gap-x-2">
							<MitraButton
								:text="$t('GLOBAL.actions.edit')"
								text-color="var(--grey-700)"
								outlined
								@click="editForeignKey(fk)"
							/>
							<MitraButton
								:text="$t('GLOBAL.actions.remove')"
								text-color="var(--grey-700)"
								outlined
								@click="removeForeignKey(fk.id)"
							/>
						</div>
					</div>
					<div class="mt-2 flex flex-col gap-1">
						<div
							v-for="mapping in fk.columns"
							:key="mapping.id"
							class="flex items-center text-sm text-grey-500"
						>
							<span>{{ mapping.localColumn }}</span>
							<svg-icon
								type="mdi"
								:path="mdiArrowRight"
								size="16"
								class="mx-2"
							/>
							<span>{{ mapping.foreignColumn }}</span>
						</div>
					</div>
				</div>
			</div>

			<div class="mt-2 p-3 border border-dashed border-grey-300 rounded-lg">
				<MitraButton
					:text="$t('TABLE_ADD_MENU.add_foreign_key_connection')"
					color="var(--grey-700)"
					class="w-fit mx-auto"
					outlined
					text-color="var(--grey-700)"
					@click="addForeignKeyConnection"
				/>
			</div>
		</div>
		<BaseContentFooter
			container-class="!p-4 mt-auto border-t border-grey-200 flex-shrink-0"
			:loading="loading"
			:confirm-text="
				$t('GLOBAL.actions.create') || $t('TABLE_ADD_MENU.create_table_action_fallback')
			"
			:confirm-props="{
				loadingClass: '!w-[71px] justify-center flex h-[22px] items-center'
			}"
			:confirm-disabled="
				columns.length === 0 || columns.some((c) => !c.name || !c.type)
			"
			@confirm="submit"
			@cancel="closeModal()"
		/>
	</div>
	<a-modal
		v-model:visible="connectionModal"
		destroy-on-close
		:footer="null"
		width="450px"
		centered
		:closable="false"
		class="create-project-modal"
	>
		<ForeignKeyMenu
			:name="tableName"
			:columns="columns"
			:foreign-key-data="foreignKeyToEdit"
			:used-foreign-key-columns="columns.filter(col => foreignKeys.filter(fk => !foreignKeyToEdit || fk.id !== foreignKeyToEdit.id).some(fk => fk.columns.some((c: any) => c.localColumn === col.name))).map(col => col.name)"
			@update="setForeignKeys"
			@cancel="connectionModal = false"
		/>
	</a-modal>
</template>

<script lang="ts" setup>
	import { HugeiconsIcon } from '@hugeicons/vue';
	import {
		HelpCircleIcon,
		Link03Icon,
		Settings01Icon
	} from '@hugeicons-pro/core-stroke-rounded';
	import { mdiClose, mdiDragVertical, mdiArrowRight } from '@mdi/js';
	import DraggableWrapper from '~/components/wrappers_rename/DraggableWrapper.vue';
	import DroppableWrapper from '~/components/wrappers_rename/DroppableWrapper.vue';
	import toastNotify from '~/helpers/toast_notify';

	const metadataStore = useMetadataStore();

	const maxCharacters = '';
	const tableName = ref('');
	const connectionModal = ref(false);
	const loading = ref(false);
	const foreignKeyToEdit = ref<any>(null);

	const columns = ref<
		Array<{
			id: number;
			name: string;
			type: string | null;
			fieldSize: string | null;
			defaultValue: string;
			isPrimary: boolean;
			isUnique: boolean;
			isNullable: boolean;
			autoIncrement: boolean;
		}>
	>([
		{
			id: Date.now(),
			name: 'ID',
			type: 'INT',
			defaultValue: '',
			isPrimary: true,
			isUnique: false,
			isNullable: false,
			autoIncrement: true,
			fieldSize: null
		}
	]);

	const dataTypes = ref([
		'DATE',
		'TEXT',
		'INT',
		'FLOAT',
		'BOOLEAN'
		// , 'VARCHAR'
	]);

	const foreignKeys = ref<any[]>([]);

	const isColumnForeignKey = (columnName: string) => {
		if (!columnName) return false;
		return foreignKeys.value.some((fk) =>
			fk.columns.some((c: any) => c.localColumn === columnName)
		);
	};

	const addColumn = () => {
		columns.value.push({
			id: Date.now(),
			name: '',
			type: null,
			defaultValue: '',
			isPrimary: false,
			isUnique: false,
			isNullable: true,
			autoIncrement: false,
			fieldSize: null
		});
	};

	const removeColumn = (index: number) => {
		columns.value.splice(index, 1);
	};

	const addForeignKeyConnection = () => {
		foreignKeyToEdit.value = null;
		connectionModal.value = true;
	};

	const editForeignKey = (fk: any) => {
		foreignKeyToEdit.value = JSON.parse(JSON.stringify(fk));
		connectionModal.value = true;
	};

	const removeForeignKey = (fkId: number) => {
		foreignKeys.value = foreignKeys.value.filter((item) => item.id !== fkId);
	};

	function closeModal() {
		useDialogStore().switchDialog('newTableFreeDbController', false);
	}

	function setForeignKeys(data: any) {
		if (foreignKeyToEdit.value) {
			const index = foreignKeys.value.findIndex(
				(fk) => fk.id === foreignKeyToEdit.value.id
			);
			if (index !== -1) {
				foreignKeys.value[index] = { ...foreignKeys.value[index], ...data };
			}
		} else {
			foreignKeys.value.push({ id: Date.now(), ...data });
		}
		connectionModal.value = false;
		foreignKeyToEdit.value = null;
	}

	function onColumnTypeChange(column: any) {
		if (column.type !== 'INT') {
			column.autoIncrement = false;
		}
		column.defaultValue = null;
	}

	function moveColumnToGroup(column: any, toPrimary: boolean) {
		const idx = columns.value.findIndex((c) => c.id === column.id);
		if (idx === -1) return;
		columns.value.splice(idx, 1);
		if (toPrimary) {
			const firstNonPrimary = columns.value.findIndex((c) => !c.isPrimary);
			if (firstNonPrimary === -1) {
				columns.value.push(column);
			} else {
				columns.value.splice(firstNonPrimary, 0, column);
			}
		} else {
			columns.value.push(column);
		}
	}
	function handleKeydown(
		event: KeyboardEvent,
		column: (typeof columns.value)[0],
		force = false
	) {
		if ((column.type === 'INT' || force) && event.key === '.') {
			event.preventDefault();
		}
	}

	function onPrimaryChange(column: any) {
		column.isPrimary = !column.isPrimary;
		if (column.isPrimary) {
			column.isNullable = false;
			column.isUnique = false;
			column.defaultValue = '';
		} else {
			column.autoIncrement = false;
		}
		moveColumnToGroup(column, column.isPrimary);
	}

	const primaryColumns = computed(() => columns.value.filter((c) => c.isPrimary));
	const nonPrimaryColumns = computed(() =>
		columns.value.filter((c) => !c.isPrimary)
	);

	function getColumnIndex(column: any) {
		return columns.value.findIndex((c) => c.id === column.id);
	}

	const dragOverIndex = ref<number | null>(null);
	const dragOverIsPrimary = ref<boolean>(true);
	const isDragging = ref(false);

	function onDragOverColumn(event: DragEvent, isPrimary: boolean) {
		event.preventDefault();
		isDragging.value = true;
		dragOverIsPrimary.value = isPrimary;
		const grids = Array.from(
			(event.currentTarget as HTMLElement).querySelectorAll('.grid')
		);
		const rects = grids.map((el: any) => el.getBoundingClientRect());
		const mouseY = event.clientY;
		let overIndex = 0;
		for (let i = 0; i < rects.length; i++) {
			if (mouseY < rects[i].top + rects[i].height / 2) {
				overIndex = i;
				break;
			}
			overIndex = i + 1;
		}
		dragOverIndex.value = overIndex;
	}

	function onDragLeaveColumn(event?: DragEvent) {
		if (
			!event ||
			!event.currentTarget ||
			!event.relatedTarget ||
			!(event.currentTarget as HTMLElement).contains(event.relatedTarget as Node)
		) {
			dragOverIndex.value = null;
			isDragging.value = false;
		}
	}

	function onDropColumn(event: DragEvent, isPrimary: boolean) {
		event.preventDefault();
		isDragging.value = false;
		const data = event.dataTransfer?.getData('value');
		if (!data) return;
		const { column: draggedColumn } = JSON.parse(data).data;
		const fromIndex = columns.value.findIndex((c) => c.id === draggedColumn.id);
		if (fromIndex === -1) return;
		if (!!columns.value[fromIndex].isPrimary !== isPrimary) return;
		const groupIndexes = columns.value
			.map((c, idx) => ({ c, idx }))
			.filter(({ c }) => !!c.isPrimary === isPrimary)
			.map(({ idx }) => idx);
		const toGlobalIndex =
			groupIndexes[dragOverIndex.value ?? 0] ??
			(isPrimary ? 0 : columns.value.length);
		if (fromIndex !== toGlobalIndex && fromIndex !== toGlobalIndex - 1) {
			const [removed] = columns.value.splice(fromIndex, 1);
			columns.value.splice(
				toGlobalIndex > fromIndex ? toGlobalIndex - 1 : toGlobalIndex,
				0,
				removed
			);
		}
		dragOverIndex.value = null;
	}

	async function submit() {
		loading.value = true;
		const jdbc = useConnectionStore().selectedJdbcProperties?.id;
		if (jdbc) {
			const { error } = await useFreeDbService().createFreeDbTable(
				useCloneDeep({
					tableName: tableName.value,
					columns: columns.value,
					primaryKeyColumns: columns.value
						.filter((column) => column.isPrimary)
						.map((column) => column.name),
					foreignKeys: foreignKeys.value.map((fkGroup: any) => ({
						columnNames: fkGroup.columns.map((c: any) => c.localColumn),
						referencedColumns: fkGroup.columns.map((c: any) => c.foreignColumn),
						referencedTableName: fkGroup.table
					}))
				}),
				jdbc
			);

			if (!error) {
				metadataStore.addOnMetadataList({
					id: tableName.value,
					name: tableName.value,
					dataSourceId: jdbc,
					isFreeDb: true
				});
				metadataStore.fetchDatabaseList();
				closeModal();
			} else toastNotify((error as CustomError).message);
		}
		loading.value = false;
	}
</script>

<style scoped>
	.button-panel {
		margin-top: 8px;
	}
	.cancel {
		margin-right: 22px;
		color: #5d6585;
		font-size: 14px;
		font-weight: 500;
		cursor: pointer;
		margin-left: auto;
	}

	.chip {
		border: 1px solid #c7cbdd;
		border-radius: 100px;
		display: flex;
		align-items: center;
		padding: 4px 14px 4px 10px;
		color: #5d6585;
		height: 36px;
		width: fit-content;
		font-size: 14px;
	}

	.container-list {
		overflow-y: auto;
		width: 100%;
	}

	:deep(.imported-file-name-box) {
		margin-right: 16px;
	}

	.container {
		max-height: 248px;
		width: 244px;
		padding: 5px 4px;
		display: flex;
		margin: 0px -16px -10px -16px;
	}
	.search-input {
		margin: 0px;
	}
	.item span {
		font-style: normal;
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 14px;
		font-weight: 500;
		margin-left: 10px;
		width: -webkit-fill-available;
	}

	.item {
		padding-left: 8px;
		padding-right: 8px;
		margin-top: 2px;
		margin-bottom: 8px;
		margin-right: 2px;
		margin-left: 4px;
		border-radius: 8px;
		height: 32px;
		color: var(--grey-700);
		white-space: nowrap;
		display: flex;
		align-items: center;
	}

	.item:not(.dataset):hover {
		background-color: #e7e8f0;
	}

	:deep(.ant-btn) span {
		color: white !important;
		font-family: roboto;
		font-weight: 500;
		font-size: 14px;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
			'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
			'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
	}
	:deep(.ant-btn-dashed) span {
		color: var(--grey-700) !important;
	}
	:deep(.ant-btn-dashed:hover) span {
		color: var(--violet-600) !important;
	}

	.title {
		color: #1b2139;
		font-weight: 500;
		font-size: 16px;
	}
	.text {
		color: var(--grey-700);
		font-weight: 500;
		font-size: 14px;
	}
	:deep(.ant-btn)[disabled] {
		background-color: #ddd6fe !important;
	}
	:deep(.ant-select-selector),
	:deep(.ant-input) {
		height: 40px !important;
	}
	.search-input :deep(.ant-input) {
		height: 32px !important;
	}
	:deep(.ant-select span) {
		font-weight: 400;
		font-size: 14px;
		display: flex;
		align-items: center;
		height: 40px;
		color: #0e1428;
		font: inherit;
		font-family: 'Inter';
	}
	.suffix {
		border-left: 1px solid var(--grey-300);
		display: flex;
		justify-content: center;
		align-items: center;
		width: 56px;
		height: 100%;
	}
	.suffix.active,
	.suffix.active svg {
		color: var(--violet-700) !important;
		background-color: var(--violet-50);
	}
	:deep(.ant-input-affix-wrapper) {
		height: 40px !important;
		padding: 0px 0px 0px 12px !important;
		overflow: hidden;
	}
	:deep(.ant-select-arrow) {
		margin-top: 0px;
		top: 0px;
		margin-right: 5px;
		transform: scale(0.5);
	}
	:deep(.ant-select-arrow:has(.enterprise-tag)) {
		right: 80px !important;
	}
	:deep(.ant-select-selection-placeholder) {
		color: #5d6585;
	}
	:deep(::-webkit-input-placeholder),
	::-webkit-input-placeholder {
		color: #5d6585;
	}
	.dimension-btn {
		display: flex;
		align-items: center;
		color: #7839ee;
		font-weight: 500;
		font-size: 14px;
		cursor: pointer;
		height: 36px;
		padding: 5px 12px;
	}

	.title-btn {
		font-family: Inter;
		font-size: 12px;
		font-weight: 400;
		line-height: 18px;
		letter-spacing: 0em;
		text-align: left;
		padding: 5px 12px;
		height: 34px;
		background-color: white !important;
		cursor: default;
	}
	.hide-span {
		max-height: 0px;
	}
	.borders {
		border-top: 1px solid #e7e8f0;
		padding: 16px 24px;
	}

	/* Custom Checkbox Styles */
	.custom-checkbox,
	:deep(.ant-checkbox) {
		max-width: 16px !important;
		max-height: 16px !important;
		border-radius: 4px !important;
		display: flex;
		position: relative;
		top: 0 !important;
	}
	:deep(.ant-checkbox-inner) {
		border-color: var(--grey-300) !important;
		background-color: white !important;
	}
	:deep(.ant-checkbox-disabled .ant-checkbox-inner) {
		background-color: var(--grey-100) !important;
	}
	:deep(.ant-checkbox-inner::after),
	:deep(.ant-checkbox-checked .ant-checkbox-inner) {
		border-width: 1px !important;
		border-color: var(--violet-600) !important;
		top: 49%;
	}

	:deep(.ant-input:focus),
	:deep(.ant-input-focused),
	:deep(.ant-select-focused .ant-select-selector),
	:deep(.ant-picker-focused) {
		border-color: var(--violet-600, #7c3aed) !important;
		box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2) !important;
	}
	:deep(.ant-input),
	:deep(.ant-picker),
	:deep(.ant-select-selector) {
		height: 40px !important;
		border-radius: 6px !important;
		border-color: var(--grey-300, #d1d5db) !important;
		display: flex;
		align-items: center;
	}
	.database-menu .ant-popover-inner-content {
		padding: 0px;
	}
</style>
