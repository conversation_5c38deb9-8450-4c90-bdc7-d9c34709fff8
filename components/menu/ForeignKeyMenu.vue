<template>
	<div
		class="foreign-key-modal flex w-[580px] min-h-[80vh] max-h-[80vh] flex-col border border-grey-200 rounded-lg shadow-md bg-white"
	>
		<!-- <PERSON>abeçal<PERSON> do Modal -->
		<div
			class="flex items-center justify-between h-[50px] px-4 borders flex-shrink-0"
		>
			<div class="title font-medium text-lg">
				{{ $t('FOREIGN_KEY_MENU.add_connection') }}
			</div>
			<svg-icon
				class="cursor-pointer text-grey-500 hover:text-grey-700"
				size="18"
				type="mdi"
				:path="mdiClose"
				@click="emit('cancel')"
			></svg-icon>
		</div>

		<!-- Corpo do Modal -->
		<div class="flex flex-col overflow-y-auto flex-grow p-6 space-y-5">
			<!-- Seletor de Esquema -->
			<!-- <div>
				<div class="text mb-[6px]">{{ $t('FOREIGN_KEY_MENU.labels.select_schema') }}</div>
				<a-select
					v-model:value="selectedSchema"
					:placeholder="$t('FOREIGN_KEY_MENU.placeholders.select_schema')"
					class="w-full"
				>
					<a-select-option
						v-for="schema_name in schemas"
						:key="schema_name"
						:value="schema_name"
					>
						{{ schema_name }}
					</a-select-option>
				</a-select>
			</div> -->

			<!-- Seletor de Tabela de Referência -->
			<div>
				<div class="text mb-[6px]">
					{{ $t('FOREIGN_KEY_MENU.labels.select_reference_table') }}
				</div>
				<a-select
					v-model:value="selectedReferenceTable"
					:placeholder="$t('FOREIGN_KEY_MENU.placeholders.select_table')"
					class="w-full"
					@change="gatTableAtributes"
				>
					<a-select-option
						v-for="table in availableTablesForSchema"
						:key="table"
						:value="table"
					>
						{{ table }}
					</a-select-option>
				</a-select>
			</div>

			<!-- Mapeamento de Colunas -->
			<div>
				<div class="text mb-[6px]">
					{{
						$t('FOREIGN_KEY_MENU.labels.select_columns_for_reference', {
							tableName: currentTableName
						})
					}}
				</div>
				<!-- Cabeçalhos do Mapeamento -->
				<div class="grid grid-cols-[1fr_20px_1fr_18px] gap-x-3 mb-2 px-1 items-center">
					<div class="text-xs text-[var(--grey-500)] font-medium">
						{{ currentTableName }}
					</div>
					<div></div>
					<!-- Espaço para a seta -->
					<div class="text-xs text-[var(--grey-500)] font-medium">
						{{
							selectedReferenceTable ||
							$t('FOREIGN_KEY_MENU.headers.reference_table_fallback')
						}}
					</div>
					<div></div>
					<!-- Espaço para o botão de fechar -->
				</div>

				<!-- Lista de Mapeamentos -->
				<div class="flex flex-col space-y-2">
					<div
						v-for="(mapping, index) in columnMappings"
						:key="mapping.id"
						class="grid grid-cols-[1fr_20px_1fr_18px] gap-x-3 items-center"
					>
						<a-select
							v-model:value="mapping.localColumn"
							:placeholder="$t('FOREIGN_KEY_MENU.placeholders.select')"
							class="w-full"
							:disabled="!!columnName"
						>
							<a-select-option
								v-for="column in availableColumnsForLocalTable"
								:key="`local-${column?.name}`"
								:value="column?.name"
							>
								{{ column?.name }}
							</a-select-option>
						</a-select>
						<svg-icon
							type="mdi"
							:path="mdiArrowRight"
							size="20"
							class="text-grey-500 justify-self-center"
						/>
						<a-select
							v-model:value="mapping.foreignColumn"
							:placeholder="$t('FOREIGN_KEY_MENU.placeholders.select')"
							class="w-full"
							:disabled="!selectedReferenceTable"
						>
							<a-select-option
								v-for="colName in availableColumnsForForeignKeyFiltered"
								:key="`foreign-${colName}`"
								:value="colName"
							>
								{{ colName }}
							</a-select-option>
						</a-select>
						<svg-icon
							class="cursor-pointer text-grey-500 hover:text-grey-700 justify-self-center"
							size="18"
							type="mdi"
							:path="mdiClose"
							@click="removeColumnMapping(index)"
						></svg-icon>
					</div>
				</div>

				<!-- Botão Adicionar Linha de Mapeamento -->
				<div
					v-if="!(columnName && columnMappings.length > 0)"
					class="mt-3 p-3 border border-dashed border-[var(--grey-300)] rounded-lg flex justify-center"
				>
					<MitraButton
						:text="$t('FOREIGN_KEY_MENU.buttons.add_column')"
						color="var(--grey-700)"
						class="w-fit mx-auto"
						outlined
						text-color="var(--grey-700)"
						@click="addColumnMapping"
					/>
				</div>
			</div>
		</div>

		<BaseContentFooter
			container-class="!p-4 mt-auto border-t border-grey-200 flex-shrink-0"
			:loading="loading"
			:confirm-text="$t('BUTTONS.save')"
			@confirm="submitForeignKey"
			@cancel="emit('cancel')"
		/>
	</div>
</template>
<script lang="ts" setup>
	import { mdiClose, mdiArrowRight } from '@mdi/js';
	import { i18n } from '~~/plugins/i18n';

	const metadataStore = useMetadataStore();

	const props = defineProps({
		name: {
			type: String,
			default: ''
		},
		columnName: {
			type: String,
			default: ''
		},
		columns: {
			type: Array,
			default: () => []
		},
		foreignKeyData: {
			type: Object,
			default: null
		},
		usedForeignKeyColumns: {
			type: Array,
			default: () => []
		}
	});

	const emit = defineEmits(['cancel', 'update']);

	const selectedReferenceTable = ref<string | null>(null);
	const currentTableName = ref(props.name);

	interface ColumnMapping {
		id: number;
		localColumn: string | null;
		foreignColumn: string | null;
	}

	const columnMappings = ref<ColumnMapping[]>([]);

	watch(
		() => props.foreignKeyData,
		(newData) => {
			if (newData) {
				selectedReferenceTable.value = newData.table;
				columnMappings.value = newData.columns;
				gatTableAtributes();
			} else {
				selectedReferenceTable.value = null;
				columnMappings.value = [];
			}
		},
		{ immediate: true, deep: true }
	);

	const availableTablesForSchema = computed(() => {
		const group = metadataStore.databaseList?.find(
			(g) => g.groupName === i18n.global.t('GLOBAL.general')
		);
		if (group)
			return group.tableMetadataList?.map((table: any) => table?.name) || [];
		return [];
	});

	const loading = ref(false);

	const availableColumnsForForeignKey = ref([]);

	const availableColumnsForForeignKeyFiltered = computed(() => {
		const usedForeignColumns = columnMappings.value.map((m) => m.foreignColumn);
		return availableColumnsForForeignKey.value.filter(
			(col: string) => !usedForeignColumns.includes(col)
		);
	});

	const availableColumnsForLocalTable = computed(() => {
		const usedColumns = columnMappings.value.map((m) => m.localColumn);
		const usedForeignKeyColumns = props.usedForeignKeyColumns as string[];
		return (props.columns as { name: string }[]).filter(
			(col) =>
				!usedColumns.includes(col.name) && !usedForeignKeyColumns.includes(col.name)
		);
	});

	const addColumnMapping = () => {
		columnMappings.value.push({
			id: Date.now(),
			localColumn: props.columnName || null,
			foreignColumn: null
		});
	};

	const removeColumnMapping = (index: number) => {
		columnMappings.value.splice(index, 1);
	};

	async function gatTableAtributes() {
		if (selectedReferenceTable.value) {
			const { data } = await useFreeDbService().getFreeDbMetadata(
				selectedReferenceTable.value
			);
			if (data)
				availableColumnsForForeignKey.value = data.columns?.map((col: any) => col.name);
		}
	}

	const submitForeignKey = () => {
		emit('update', {
			table: selectedReferenceTable.value,
			columns: columnMappings.value
		});
	};
</script>

<style scoped>
	/* Importando variáveis de cor se não forem globais (exemplo) */
	/* @import url('path/to/your/variables.css'); */

	/* Estilos gerais baseados no exemplo fornecido */
	.title {
		color: #1b2139; /* Cor do título do exemplo */
		font-weight: 500;
		font-size: 16px; /* text-lg é 1.125rem, 16px é mais próximo do exemplo */
	}
	.text {
		color: var(--grey-700, #374151); /* Cor dos labels do exemplo */
		font-weight: 500;
		font-size: 14px;
	}

	/* Estilização para componentes Ant Design Vue */
	:deep(.ant-select-selector),
	:deep(.ant-input) {
		/* Se houver a-input */
		height: 40px !important;
		border-radius: 6px !important; /* Bordas arredondadas como no exemplo */
		border-color: var(--grey-300, #d1d5db) !important;
	}

	:deep(.ant-select-focused .ant-select-selector),
	:deep(.ant-input:focus),
	:deep(.ant-input-focused) {
		border-color: var(--violet-600, #7c3aed) !important;
		box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2) !important;
	}

	:deep(.ant-select-selection-item),
	:deep(.ant-select-selection-placeholder) {
		display: flex !important;
		align-items: center !important;
		height: 38px !important; /* Para alinhar texto verticalmente */
		line-height: 38px !important;
	}

	:deep(.ant-select-selection-placeholder) {
		color: #5d6585 !important; /* Cor do placeholder do exemplo */
	}

	:deep(.ant-select-arrow) {
		margin-top: 0px;
		top: 0px;
		margin-right: 5px;
		transform: scale(0.5);
		color: var(--grey-500, #6b7280) !important;
	}

	/* Botão "Adicionar Coluna" */
	.add-column-button {
		color: var(--grey-700, #374151) !important;
		border-color: var(--grey-700, #374151) !important;
	}
	.add-column-button:hover,
	.add-column-button:focus {
		color: var(--violet-600, #7c3aed) !important;
		border-color: var(--violet-600, #7c3aed) !important;
	}
	:deep(.add-column-button span) {
		font-weight: 500;
	}

	/* Botões do Rodapé */
	.cancel-button {
		color: #5d6585;
		font-weight: 500;
		border-color: var(--grey-300, #d1d5db);
	}
	.cancel-button:hover,
	.cancel-button:focus {
		color: var(--violet-600, #7c3aed);
		border-color: var(--violet-600, #7c3aed);
	}

	.save-button {
		background-color: var(--violet-600, #7c3aed) !important;
		border-color: var(--violet-600, #7c3aed) !important;
	}
	.save-button:hover,
	.save-button:focus {
		background-color: var(--violet-700, #6d28d9) !important;
		border-color: var(--violet-700, #6d28d9) !important;
	}
	.save-button[disabled],
	.save-button[disabled]:hover {
		background-color: #ddd6fe !important; /* Cor de desabilitado do exemplo */
		border-color: #ddd6fe !important;
		color: rgba(0, 0, 0, 0.25) !important;
	}
	:deep(.save-button span),
	:deep(.cancel-button span) {
		font-weight: 500;
		font-size: 14px;
	}
	:deep(.save-button span) {
		color: white !important;
	}
	.borders {
		border-bottom: 1px solid var(--grey-200);
	}
	:deep(.ant-select-selector),
	:deep(.ant-input) {
		height: 40px !important;
	}
	.search-input :deep(.ant-input) {
		height: 32px !important;
	}
	:deep(.ant-select span) {
		font-weight: 400;
		font-size: 14px;
		display: flex;
		align-items: center;
		height: 40px;
		color: #0e1428;
		font: inherit;
		font-family: 'Inter';
	}
</style>
