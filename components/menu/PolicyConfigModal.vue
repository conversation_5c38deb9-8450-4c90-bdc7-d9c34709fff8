<template>
	<div
		class="flex w-full flex-col border border-grey-200 rounded-2xl shadow-md bg-white"
	>
		<!-- <PERSON><PERSON><PERSON>l<PERSON> do Modal -->
		<div class="flex items-center justify-between px-4 py-3 borders flex-shrink-0">
			<div class="font-medium text-base text-grey-900">
				{{ $t('DATABASE.configure_table_policy') }}
			</div>
			<HugeiconsIcon
				:icon="Cancel01Icon"
				class="item-huge-icon"
				:size="18"
				color="var(--grey-500)"
				@click="closeDialog()"
			/>
		</div>

		<!-- Corpo do Modal -->
		<div class="flex flex-col overflow-y-auto flex-grow p-6 space-y-5 w-full">
			<div
				v-if="loading"
				class="flex h-full w-full overflow-hidden min-h-[212px] max-h-[70vh] relative"
			>
				<BaseMitraOverLoading />
			</div>
			<SqlEditor
				v-else
				:key="editorKey"
				class="flex h-full w-full overflow-hidden min-h-[212px] max-h-[70vh]"
				:query="currentQuery"
				@query="updateQuery"
			></SqlEditor>
		</div>

		<BaseContentFooter
			container-class="!p-4 mt-auto border-t border-grey-200 flex-shrink-0"
			:confirm-text="$t('BUTTONS.save')"
			:confirm-props="{
				loadingClass: '!w-[71px] justify-center flex h-[22px] items-center'
			}"
			@confirm="handleConfirm()"
			@cancel="closeDialog()"
		/>
	</div>
</template>

<script lang="ts" setup>
	import { Cancel01Icon } from '@hugeicons-pro/core-stroke-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';

	const metadataStore = useMetadataStore();
	const connectionStore = useConnectionStore();
	const { selectedJdbcProperties } = storeToRefs(connectionStore);

	const activeTab = computed(() => metadataStore.activeIndex);

	const currentQuery = ref('');
	const isToCreate = ref(false);
	const editorKey = ref(0);
	const currentPoilicyData = ref();
	const loading = ref(false);

	initData();

	async function initData() {
		loading.value = true;
		if (selectedJdbcProperties.value.id) {
			const { data } = await useFreeDbService().getFreeDbPolicyTableData(
				`${metadataStore.activeMetadataList[activeTab.value].id}`,
				selectedJdbcProperties.value.id
			);

			if (data.length && data[0].tableName) {
				currentQuery.value = data[0].criteria;
				currentPoilicyData.value = data[0];
				isToCreate.value = false;
				editorKey.value++;
			} else {
				isToCreate.value = true;
			}
		}
		loading.value = false;
	}

	function updateQuery(value: string) {
		currentQuery.value = value;
	}

	function closeDialog() {
		useDialogStore().switchDialog('policyConfigController', false);
	}

	function handleConfirm() {
		const createPolicyPayload: PolicyData = {
			tableName: metadataStore.activeMetadataList[activeTab.value].id,
			description: '',
			criteria: currentQuery.value,
			jdbcConnectionConfigId: selectedJdbcProperties.value.id ?? -1
		};

		if (isToCreate.value) {
			useFreeDbService().createFreeDbPolicy(createPolicyPayload);
		} else {
			useFreeDbService().updateFreeDbPolicy(
				createPolicyPayload,
				currentPoilicyData.value.id
			);
		}
		closeDialog();
	}
</script>

<style scoped>
	.borders {
		border-bottom: 1px solid var(--grey-200, #e5e7eb);
	}
</style>
