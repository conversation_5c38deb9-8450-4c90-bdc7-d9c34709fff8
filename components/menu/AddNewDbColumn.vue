<template>
	<div
		class="create-column-modal flex w-[450px] min-h-[auto] max-h-[calc(100vh-80px)] flex-col border border-grey-200 rounded-lg shadow-md bg-white"
	>
		<!-- Cabeçalho do Modal -->
		<div
			class="flex items-center justify-between h-[50px] px-4 borders flex-shrink-0"
		>
			<div class="title font-medium text-lg">
				{{
					useMetadataStore().colToEdit
						? $t('CREATE_COLUMN_MODAL.edit_column_title')
						: $t('CREATE_COLUMN_MODAL.create_new_column_title')
				}}
			</div>
			<svg-icon
				class="cursor-pointer text-grey-500 hover:text-grey-700"
				size="18"
				type="mdi"
				:path="mdiClose"
				@click="closeDialog()"
			></svg-icon>
		</div>

		<!-- Corpo do Modal -->
		<div class="flex flex-col overflow-y-auto flex-grow p-6 space-y-5">
			<!-- Nome da Coluna -->
			<div>
				<div class="text mb-[6px]">{{ $t('CREATE_COLUMN_MODAL.labels.name') }}</div>
				<a-input
					ref="inputNameRef"
					v-model:value="columnData.name"
					v-focus
					:placeholder="$t('CREATE_COLUMN_MODAL.placeholders.column_name')"
					class="w-full"
					:maxlength="63"
				/>
			</div>

			<!-- Tipo da Coluna -->
			<div>
				<div class="text mb-[6px]">{{ $t('CREATE_COLUMN_MODAL.labels.type') }}</div>
				<a-select
					v-model:value="columnData.type"
					:placeholder="$t('CREATE_COLUMN_MODAL.placeholders.select')"
					class="w-full"
					@change="onColumnTypeChange"
				>
					<a-select-option
						v-for="dataType in availableDataTypes"
						:key="dataType"
						:value="dataType"
					>
						{{ dataType }}
					</a-select-option>
				</a-select>
			</div>

			<!-- Valor Padrão -->
			<div>
				<div class="text mb-[6px]">
					{{ $t('CREATE_COLUMN_MODAL.labels.default_value') }}
				</div>
				<a-input
					v-if="columnData.type !== 'DATE' && columnData.type !== 'BOOLEAN'"
					v-model:value="columnData.defaultValue"
					:type="['INT', 'FLOAT'].includes(columnData.type) ? 'number' : 'text'"
					:placeholder="$t('CREATE_COLUMN_MODAL.placeholders.null')"
					class="w-full"
					@keydown="handleKeydown"
				/>
				<a-date-picker
					v-else-if="columnData.type === 'DATE'"
					v-model:value="columnData.defaultValue"
					:placeholder="$t('GLOBAL.insert')"
					class="w-full"
					value-format="DD-MM-YYYY"
				/>
				<a-checkbox
					v-else-if="columnData.type === 'BOOLEAN'"
					v-model:checked="columnData.defaultValue"
				/>
			</div>

			<!-- Conexão com outra tabela (Foreign key) -->
			<div>
				<div class="text mb-[6px]">
					{{ $t('CREATE_COLUMN_MODAL.foreign_key_section_title') }}
				</div>
				<div
					v-if="foreignKeys.length > 0"
					class="flex flex-col gap-y-2"
				>
					<div
						v-for="fk in foreignKeys"
						:key="fk.id"
						class="p-3 border border-solid border-grey-200 rounded-lg"
					>
						<div class="flex justify-between items-center">
							<div class="text-sm font-medium">
								{{ $t('TABLE_ADD_MENU.relation_with') }}
								<span class="font-semibold">{{ fk.table }}</span>
							</div>
							<div class="flex items-center gap-x-2">
								<MitraButton
									:text="$t('GLOBAL.actions.edit')"
									text-color="var(--grey-700)"
									outlined
									@click="editForeignKey(fk)"
								/>
								<MitraButton
									:text="$t('GLOBAL.actions.remove')"
									text-color="var(--grey-700)"
									outlined
									@click="removeForeignKey(fk.id)"
								/>
							</div>
						</div>
						<div class="mt-2 flex flex-col gap-1">
							<div
								v-for="mapping in fk.columns"
								:key="mapping.id"
								class="flex items-center text-sm text-grey-500"
							>
								<span>{{ mapping.localColumn }}</span>
								<svg-icon
									type="mdi"
									:path="mdiArrowRight"
									size="16"
									class="mx-2"
								/>
								<span>{{ mapping.foreignColumn }}</span>
							</div>
						</div>
					</div>
				</div>
				<div
					v-if="!foreignKeys.length"
					class="mt-1 p-3 border border-dashed border-[var(--grey-300)] rounded-lg flex justify-center"
				>
					<MitraButton
						:text="
							foreignKeys.length
								? $t('CREATE_COLUMN_MODAL.edit_connection')
								: $t('CREATE_COLUMN_MODAL.add_connection')
						"
						color="var(--grey-700)"
						class="w-fit mx-auto"
						outlined
						text-color="var(--grey-700)"
						@click="openForeignKeyModal"
					/>
				</div>
			</div>

			<!-- Restrições -->
			<div>
				<div class="text mb-[10px]">
					{{ $t('CREATE_COLUMN_MODAL.constraints_section_title') }}
				</div>
				<div class="space-y-4">
					<!-- Primária -->
					<div class="flex items-start space-x-3">
						<a-checkbox
							v-model:checked="columnData.isPrimary"
							class="custom-checkbox mt-1"
							@change="() => onPrimaryChange()"
						></a-checkbox>
						<div>
							<div class="text font-medium">
								{{ $t('CREATE_COLUMN_MODAL.constraints.primary.label') }}
							</div>
							<div class="text-xs text-grey-500">
								{{ $t('CREATE_COLUMN_MODAL.constraints.primary.description') }}
							</div>
						</div>
					</div>
					<!-- Única -->
					<div class="flex items-start space-x-3">
						<a-checkbox
							v-model:checked="columnData.isUnique"
							:disabled="columnData.isPrimary"
							class="custom-checkbox mt-1"
						></a-checkbox>
						<div>
							<div class="text font-medium">
								{{ $t('CREATE_COLUMN_MODAL.constraints.unique.label') }}
							</div>
							<div class="text-xs text-grey-500">
								{{ $t('CREATE_COLUMN_MODAL.constraints.unique.description') }}
							</div>
						</div>
					</div>
					<!-- Obrigatória -->
					<div class="flex items-start space-x-3">
						<a-checkbox
							v-model:checked="columnData.isRequired"
							:disabled="columnData.isPrimary"
							class="custom-checkbox mt-1"
						></a-checkbox>
						<div>
							<div class="text font-medium">
								{{ $t('CREATE_COLUMN_MODAL.constraints.required.label') }}
							</div>
							<div class="text-xs text-grey-500">
								{{ $t('CREATE_COLUMN_MODAL.constraints.required.description') }}
							</div>
						</div>
					</div>
					<!-- Autoincrement -->
					<div class="flex items-start space-x-3">
						<a-checkbox
							v-model:checked="columnData.autoIncrement"
							:disabled="!columnData.isUnique || columnData.type !== 'INT'"
							class="custom-checkbox mt-1"
						/>
						<div>
							<div class="text font-medium">Autoincrement</div>
							<div class="text-xs text-grey-500">
								{{ $t('CREATE_COLUMN_MODAL.constraints.auto_increment.description') }}
							</div>
						</div>
					</div>
				</div>
				<div class="mt-4">
					<div class="text mb-[6px]">{{ $t('CREATE_COLUMN_MODAL.field_size') }}</div>
					<a-input
						ref="inputNameRef"
						v-model:value="columnData.fieldSize"
						v-focus
						:disabled="columnData.type !== 'VARCHAR'"
						:placeholder="$t('DRIVE.size')"
						class="w-full"
						:maxlength="63"
						@keydown="handleKeydown($event, true)"
					/>
				</div>
			</div>
		</div>

		<BaseContentFooter
			container-class="!p-4 mt-auto border-t border-grey-200 flex-shrink-0"
			:loading="loading"
			:confirm-text="
				useMetadataStore().colToEdit
					? $t('GLOBAL.actions.edit')
					: $t('CREATE_COLUMN_MODAL.actions.create_fallback')
			"
			:confirm-props="{
				loadingClass: '!w-[71px] justify-center flex h-[22px] items-center'
			}"
			:confirm-disabled="isCreateDisabled"
			@confirm="submitColumn"
			@cancel="closeDialog()"
		/>
	</div>
	<a-modal
		v-model:visible="connectionModal"
		destroy-on-close
		:footer="null"
		width="450px"
		centered
		:closable="false"
		class="create-project-modal"
	>
		<ForeignKeyMenu
			:name="activeTable.id"
			:column-name="columnData.name"
			:foreign-key-data="foreignKeyToEdit"
			@update="handleForeignKeyUpdate"
			@cancel="connectionModal = false"
		/>
	</a-modal>
</template>

<script lang="ts" setup>
	import { mdiClose, mdiArrowRight } from '@mdi/js';
	import toastNotify from '~/helpers/toast_notify';

	const dialogStore = useDialogStore();
	const freeDbService = useFreeDbService();
	const connectionStore = useConnectionStore();
	const metadataStore = useMetadataStore();
	const metadataList = computed(() => metadataStore.activeMetadataList);
	const metadataIndex = computed(() => metadataStore.activeIndex);
	const activeTable = computed(() => metadataList.value[metadataIndex.value]);

	const inputNameRef = ref<HTMLInputElement | null>(null);
	const loading = ref(false);
	const connectionModal = ref(false);
	const foreignKeyToEdit = ref<any>(null);
	const foreignKeys = ref<any[]>([]);

	const columnData = ref({
		name: '',
		type: 'TEXT',
		defaultValue: null,
		isPrimary: false,
		isUnique: false,
		isRequired: false,
		autoIncrement: false,
		fieldSize: null
	});

	const availableDataTypes = ref([
		'DATE',
		'TEXT',
		'INT',
		'FLOAT',
		'BOOLEAN',
		'VARCHAR'
	]);

	const isCreateDisabled = computed(() => {
		return !columnData.value.name || !columnData.value.type || loading.value;
	});

	setInitialContent();

	function handleKeydown(event: KeyboardEvent, force = false) {
		if ((columnData.value.type === 'INT' || force) && event.key === '.') {
			event.preventDefault();
		}
	}

	function onPrimaryChange() {
		if (columnData.value.isPrimary) {
			columnData.value.isRequired = true;
			columnData.value.isUnique = true;
			columnData.value.defaultValue = null;
		} else {
			columnData.value.autoIncrement = false;
		}
	}

	function onColumnTypeChange() {
		if (columnData.value.type !== 'INT') {
			columnData.value.autoIncrement = false;
		}
		columnData.value.defaultValue = null;
	}

	const openForeignKeyModal = () => {
		foreignKeyToEdit.value = null;
		connectionModal.value = true;
	};

	const handleForeignKeyUpdate = (data: any) => {
		if (foreignKeyToEdit.value) {
			const index = foreignKeys.value.findIndex(
				(fk) => fk.id === foreignKeyToEdit.value.id
			);
			if (index !== -1) {
				foreignKeys.value[index] = { ...foreignKeys.value[index], ...data };
			}
		} else {
			foreignKeys.value.push({ id: Date.now(), ...data });
		}
		connectionModal.value = false;
		foreignKeyToEdit.value = null;
	};

	const removeForeignKey = (fkId: number) => {
		foreignKeys.value = foreignKeys.value.filter((fk) => fk.id !== fkId);
	};

	const editForeignKey = (fk: any) => {
		foreignKeyToEdit.value = JSON.parse(JSON.stringify(fk));
		connectionModal.value = true;
	};

	const closeDialog = () => {
		dialogStore.switchDialog('databaseColumnController', false);
	};

	const submitColumn = async () => {
		if (isCreateDisabled.value) return;

		loading.value = true;

		const jdbcId = connectionStore.selectedJdbcProperties?.id;
		if (!jdbcId) {
			loading.value = false;
			return;
		}

		let fkPayload = null;
		if (foreignKeys.value.length) {
			fkPayload = foreignKeys.value.map((fk) => ({
				columnNames: [columnData.value.name],
				referencedTableName: fk.table,
				referencedColumns: [fk?.columns[0].foreignColumn]
			}));
		}

		const colToEdit = useMetadataStore().colToEdit;
		const payload = {
			tableName: activeTable.value.id,
			name: columnData.value.name,
			type: String(columnData.value.type).toUpperCase(),
			isNullable: !columnData.value.isRequired,
			isAutoIncrement: columnData.value.autoIncrement,
			isUnique: columnData.value.isUnique,
			defaultValue: columnData.value.defaultValue || null,
			isPrimaryKey: columnData.value.isPrimary,
			foreignKey: fkPayload && fkPayload.length ? fkPayload : undefined,
			oldName: colToEdit?.name || undefined
		};

		const method = colToEdit
			? freeDbService.updateFreeDbColumn
			: freeDbService.createFreeDbColumn;
		const { error } = await method(payload, jdbcId);
		if (error) {
			toastNotify(
				(error as CustomError).message.includes('Invalid use of NULL value')
					? `column "${columnData.value.name}" of relation "${activeTable.value.id}" contains null values`
					: (error as CustomError).message
			);
		} else {
			useMetadataStore().fetchDatabaseList();
			useMetadataStore().updateActiveTableContent();
			closeDialog();
		}
		loading.value = false;
	};

	function setInitialContent() {
		const header = useMetadataStore().colToEdit;
		if (header) {
			columnData.value = {
				name: header.name || '',
				type: header.columnType || '',
				defaultValue: header.defaultValue || header.columnDefault || null,
				isPrimary: header.inPrimaryKey || false,
				isUnique: header.uniqueKeyName || false,
				isRequired: header.isRequired || false,
				autoIncrement: header.autoIncrement || false,
				fieldSize: header.fieldSize || null
			};

			if (header.referencedTable && header.referencedColumn) {
				foreignKeys.value = [
					{
						id: Date.now(),
						table: header.referencedTable,
						columns: [
							{
								id: Date.now(),
								localColumn: header.name,
								foreignColumn: header.referencedColumn
							}
						],
						fkName: header.referencedFkName || null,
						descrColumn: header.referencedDescrColumnName || null
					}
				];
			} else {
				foreignKeys.value = [];
			}
		}
	}
</script>

<style scoped>
	.title {
		color: #1b2139;
		font-weight: 500;
		font-size: 16px;
	}
	.text {
		color: var(--grey-700, #374151);
		font-weight: 500;
		font-size: 14px;
	}
	.borders {
		border-bottom: 1px solid var(--grey-200, #e5e7eb);
	}

	/* Estilização para componentes Ant Design Vue */
	:deep(.ant-input),
	:deep(.ant-picker),
	:deep(.ant-select-selector) {
		height: 40px !important;
		border-radius: 6px !important;
		border-color: var(--grey-300, #d1d5db) !important;
		display: flex;
		align-items: center;
	}
	:deep(.ant-input::placeholder),
	:deep(.ant-select-selection-placeholder) {
		color: #5d6585 !important;
	}
	:deep(.ant-select-selection-item) {
		display: flex !important;
		align-items: center !important;
		height: 38px !important;
		line-height: 38px !important;
	}

	:deep(.ant-input:focus),
	:deep(.ant-input-focused),
	:deep(.ant-select-focused .ant-select-selector),
	:deep(.ant-picker-focused) {
		border-color: var(--violet-600, #7c3aed) !important;
		box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2) !important;
	}

	:deep(.ant-select-arrow) {
		margin-top: 0px;
		top: 0px;
		margin-right: 5px;
		transform: scale(0.5);
		color: var(--grey-500, #6b7280) !important;
	}

	.custom-checkbox,
	:deep(.ant-checkbox) {
		max-width: 16px !important;
		max-height: 16px !important;
		border-radius: 4px !important;
		display: flex;
		position: relative;
		top: 0 !important;
	}
	:deep(.ant-checkbox-inner) {
		border-color: var(--grey-300) !important;
		background-color: white !important;
	}
	:deep(.ant-checkbox-disabled .ant-checkbox-inner) {
		background-color: var(--grey-100) !important;
	}
	:deep(.ant-checkbox-inner::after),
	:deep(.ant-checkbox-checked .ant-checkbox-inner) {
		border-width: 1px !important;
		border-color: var(--violet-600) !important;
		top: 49%;
	}
	:deep(.ant-select-selector),
	:deep(.ant-input) {
		height: 40px !important;
	}
	.search-input :deep(.ant-input) {
		height: 32px !important;
	}
	:deep(.ant-select span) {
		font-weight: 400;
		font-size: 14px;
		display: flex;
		align-items: center;
		height: 40px;
		color: #0e1428;
		font: inherit;
		font-family: 'Inter';
	}
</style>
