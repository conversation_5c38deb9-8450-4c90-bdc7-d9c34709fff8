<template>
	<div
		class="h-[576px] w-[820px] bg-white rounded-xl shadow-2xl flex overflow-hidden transform transition-all"
	>
		<!-- <PERSON><PERSON> Esquerda -->
		<div class="flex flex-col bg-grey-200 min-w-[300px]">
			<div class="px-6 py-8">
				<h1 class="text-3xl font-bold mb-4 text-grey-900">
					{{ $t('AI.how_it_works') }}
				</h1>
				<p class="text-sm mb-auto leading-relaxed text-grey-500">
					{{ $t('AI.how_to_use') }}
				</p>
			</div>
		</div>

		<!-- Coluna Direita -->
		<div class="flex flex-col relative bg-white">
			<div class="p-8 flex-grow overflow-y-auto">
				<h2 class="text-xl font-bold mb-3 text-grey-900">
					{{ $t('AI.how_mitra_use_gemini') }}
				</h2>
				<div>
					<p class="mb-4 text-sm font-inter text-grey-900">
						{{ $t('AI.mitra_uses_model_intro')
						}}<strong>{{ $t('AI.gemini_pro_2_5') }}</strong
						>{{ $t('AI.provided_by_google') }} {{ $t('AI.ensure_autonomy_intro')
						}}<strong>{{ $t('AI.own_api_key') }}</strong
						>{{ $t('AI.of_gemini') }} {{ $t('AI.connection_allows_intro')
						}}<!-- <strong>{{ $t('AI.requests_per_day') }}</strong> -->{{
							$t('AI.on_gemini_pro_2_5')
						}}<strong>{{ $t('AI.free_credits_amount') }}</strong
						>{{ $t('AI.from_google_valid_for_90_days') }}
					</p>

					<!-- <h4 class="mt-6 mb-3 text-sm font-semibold font-inter text-grey-900">
						{{ $t('AI.plans_how_it_applies_title') }}
					</h4>
					<ul class="list-disc pl-5 mb-4 text-sm font-inter text-grey-900 space-y-2">
						<li>
							<strong>{{ $t('AI.plan_free_label') }}</strong
							>{{ $t('AI.plan_free_details_part1')
							}}<strong>{{ $t('AI.plan_free_requests') }}</strong
							>{{ $t('AI.plan_free_details_part2') }}
						</li>
						<li>
							<strong>{{ $t('AI.plan_starter_label') }}</strong
							>{{ $t('AI.plan_starter_details_part1')
							}}<strong>{{ $t('AI.plan_starter_unlimited_ai') }}</strong
							>{{ $t('AI.plan_starter_details_part2') }}
						</li>
					</ul> -->

					<p class="mb-2 text-sm font-inter text-grey-900">
						{{ $t('AI.simple_logic_emoji')
						}}<strong>{{ $t('AI.simple_logic_title') }}</strong
						>{{ $t('AI.simple_logic_explanation_text') }}
					</p>

					<!-- <p class="text-xs font-inter text-grey-700">
						{{ $t('AI.paid_plan_gemini_limit_note') }}
					</p> -->
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup></script>

<style scoped>
	.border-footer {
		border-top: 1px solid var(--grey-200);
	}
</style>
