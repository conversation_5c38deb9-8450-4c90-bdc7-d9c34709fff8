<template>
	<div
		class="h-[576px] w-[820px] bg-white rounded-xl shadow-2xl flex overflow-hidden transform transition-all"
		:style="{
			'--project-color': useWorkspaceStore().getProjectColor
		}"
		:class="{
			playground: useAppStore().playgroundMode
		}"
	>
		<!-- <PERSON><PERSON> Esquerda -->
		<div class="flex flex-col bg-grey-200 min-w-[300px]">
			<div class="px-6 py-8">
				<h1 class="text-3xl font-bold mb-4 text-grey-900">
					{{ $t('AI.how_get_key') }}
				</h1>
				<p class="text-sm mb-auto leading-relaxed text-grey-500">
					{{ $t('AI.how_generate_key') }}
				</p>
			</div>

			<nav class="p-4 mt-auto">
				<div
					v-for="(step, index) in steps"
					:key="index"
					class="flex items-center p-3 rounded-lg cursor-pointer transition-colors duration-200"
					:class="{
						activate: activeStep === index
					}"
					@click="activeStep = index"
				>
					<span
						class="text-base font-bold mr-[16px] w-8 text-center"
						:class="[activeStep === index ? 'text-white' : 'text-grey-600']"
					>
						{{ String(index + 1).padStart(2, '0') }}
					</span>
					<span
						class="text-sm font-medium"
						:class="[activeStep === index ? 'text-white' : 'text-grey-600']"
					>
						{{ step.title }}
					</span>
				</div>
			</nav>
		</div>

		<!-- Coluna Direita -->
		<div class="flex flex-col relative bg-white">
			<div
				v-if="activeStep"
				class="p-8 flex-grow overflow-y-auto"
			>
				<h2 class="text-xl font-bold mb-3 text-grey-900">
					{{ $t('AI.enable_tier_1') }}
				</h2>
				<p class="mb-4 text-sm font-inter text-grey-900">
					{{ $t('AI.to_access_key_intro')
					}}<strong>{{ $t('AI.tier_1_level_text') }}</strong
					>{{ $t('AI.in_gemini_access_intro')
					}}<strong>{{ $t('AI.configure_billing_text') }}</strong
					>{{ $t('AI.in_section_intro')
					}}<strong>{{ $t('AI.api_plan_billing_section_text') }}</strong
					>{{ $t('AI.and_register_payment_method_part1')
					}}<strong>{{ $t('AI.no_automatic_charges_text') }}</strong
					>{{ $t('AI.and_register_payment_method_part2')
					}}<strong>{{ $t('AI.free_credit_amount_tier1') }}</strong
					>{{ $t('AI.valid_for_intro')
					}}<strong>{{ $t('AI.valid_for_90_days_text') }}</strong
					>{{ $t('AI.remains_available_text') }}
				</p>

				<!-- Placeholder para a Imagem -->
				<div class="h-[244px] flex p-4 justify-center border-carousel">
					<a-carousel
						autoplay
						class="carousel w-[424px] h-[192px]"
					>
						<div class="w-[424px] h-[192px]">
							<img
								src="@/assets/images/mockups/google_information.png"
								alt=""
								class="w-full h-full object-cover"
							/>
						</div>
						<div class="w-[424px] h-[192px]">
							<img
								src="@/assets/images/mockups/google_user.png"
								alt=""
								class="w-full h-full object-cover"
							/>
						</div>
						<div class="w-[424px] h-[192px]">
							<img
								src="@/assets/images/mockups/google_card.png"
								alt=""
								class="w-full h-full object-cover"
							/>
						</div>
					</a-carousel>
				</div>
			</div>
			<div
				v-else
				class="p-8 flex-grow overflow-y-auto"
			>
				<h2 class="text-xl font-bold mb-3 text-grey-900">
					{{ $t('AI.how_to_generate_key') }}
				</h2>
				<p class="mb-4 text-sm font-inter text-grey-900">
					{{ $t('AI.access_the_intro') }}
					<a
						href="https://aistudio.google.com/"
						target="_blank"
						rel="noopener noreferrer"
						class="underline font-medium text-violet-600"
						>{{ $t('AI.google_ai_studio_link_text') }}</a
					>
					{{ $t('AI.login_with_google_account_part1')
					}}<strong>{{ $t('AI.get_api_key_button_text') }}</strong
					>{{ $t('AI.login_with_google_account_part2')
					}}<strong>{{ $t('AI.create_api_key_button_text') }}</strong
					>{{ $t('AI.key_generation_instructions_part1')
					}}<strong>{{ $t('AI.copy_button_text') }}</strong
					>{{ $t('AI.key_generation_instructions_part2') }}
				</p>

				<!-- Placeholder para a Imagem -->
				<div class="h-[284px] flex p-4 justify-center border-carousel">
					<a-carousel
						autoplay
						class="carousel w-[424px] h-[232px]"
					>
						<div class="w-[424px] h-[232px]">
							<img
								src="@/assets/images/mockups/time_to_build.png"
								alt=""
								class="w-full h-full object-cover"
							/>
						</div>
						<div class="w-[424px] h-[232px]">
							<img
								src="@/assets/images/mockups/api_key.png"
								alt=""
								class="w-full h-full object-cover"
							/>
						</div>
						<div class="w-[424px] h-[232px]">
							<img
								src="@/assets/images/mockups/generated_key.png"
								alt=""
								class="w-full h-full object-cover"
							/>
						</div>
					</a-carousel>
				</div>
			</div>

			<!-- Rodapé com Botões -->
			<div class="mt-auto flex justify-end space-x-3 p-3 border-footer">
				<!-- Seu MitraButton para Cancelar -->
				<MitraButton
					v-if="activeStep > 0"
					:text="$t('BUTTONS.back')"
					outlined
					:text-color="'var(--grey-500)'"
					@click="handleBack"
				/>

				<!-- Botão "Próximo" como na imagem -->
				<MitraButton
					:text="$t(activeStep ? 'GLOBAL.finish' : 'BUTTONS.next')"
					:color="
						useAppStore().playgroundMode ? 'var(--project-color)' : 'var(--violet-600)'
					"
					@click="handleNext"
				/>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { i18n } from '~~/plugins/i18n';

	const handleNext = () => {
		if (activeStep.value) useDialogStore().switchDialog('aiKeyController', false);
		else activeStep.value++;
	};

	const handleBack = () => {
		activeStep.value--;
	};

	const activeStep = ref(0);

	const steps = ref([
		{ title: i18n.global.t('AI.how_to_generate_key') },
		{ title: i18n.global.t('AI.enable_tier_1') }
	]);
</script>

<style scoped>
	.carousel {
		border-radius: 10px;
		cursor: pointer;
	}
	.border-carousel {
		border: 1px solid var(--grey-200);
		border-radius: 12px;
	}
	:deep(.ant-carousel .slick-dots-bottom) {
		bottom: -30px !important;
	}
	:deep(.ant-carousel .slick-dots li.slick-active) {
		width: 16px !important;
	}
	:deep(.ant-carousel .slick-dots li button) {
		background: var(--grey-300) !important;
		height: 8px !important;
		width: 8px !important;
		border-radius: 100%;
	}
	:deep(.ant-carousel .slick-dots li.slick-active button) {
		background: var(--violet-600) !important;
	}
	.playground :deep(.ant-carousel .slick-dots li.slick-active button) {
		background: var(--project-color) !important;
	}
	:deep(.slick-list) {
		border-radius: 12px;
		overflow: hidden;
	}
	.border-footer {
		border-top: 1px solid var(--grey-200);
	}
	.activate {
		background-color: var(--violet-600) !important;
	}
	.playground .activate {
		background-color: var(--project-color) !important;
	}
</style>
