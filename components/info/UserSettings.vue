<template>
	<div class="home-content-area">
		<div class="flex h-full flex-col">
			<div class="light-border-b pb-5">
				<span class="truncate text-3xl font-medium text-grey-900">{{
					$t('LOGGED.manage_account')
				}}</span>
			</div>

			<div
				class="card-margin light-border-all mt-6 h-full overflow-hidden rounded-lg bg-white shadow-sm"
			>
				<div class="my-20px h-full overflow-auto">
					<div class="my-1 pb-16">
						<!-- Inicio do head -->
						<div class="light-border-b mt-3 flex justify-between pb-3 px-4">
							<div>
								<span class="block text-lg font-medium text-grey-900">{{
									$t('ACCOUNT.personal_information')
								}}</span>
								<span class="text-sm font-normal text-grey-500">{{
									$t('ACCOUNT.update_name_and_photo_here')
								}}</span>
							</div>

							<MitraButton
								:text="$t('BUTTONS.save')"
								color="var(--violet-600)"
								:disabled="!hasSomeChange"
								w-full
								class="max-h-[40px] max-w-[74px]"
								@click="handleSave"
							/>
						</div>
						<!-- Fim do head -->

						<!-- Body da configurações -->

						<a-form
							:model="profileFormState"
							name="user_settings_form"
							hide-required-mark
							class="overflow-auto pt-6"
							layout="vertical"
						>
							<div class="flex px-4">
								<div class="basis-2/6">
									<span class="text-sm font-medium text-grey-700">{{ $t('GLOBAL.name') }}</span>
								</div>

								<a-form-item
									name="name"
									class="!mb-0 basis-3/6 py-1.5"
									v-bind="validateInfos.name"
								>
									<a-input
										v-model:value="profileFormState.name"
										:disabled="!isInternalUser"
										:placeholder="$t('LOGIN.enter_name')"
										class="py-1.5"
									>
									</a-input>
								</a-form-item>

								<div
									id="div fantasma para dar espaço"
									class="basis-1/6"
								></div>
							</div>
							<div class="flex pt-6 px-4 light-border-b pb-4">
								<div class="basis-2/6">
									<span class="block text-sm font-medium text-grey-700">{{
										$t('ACCOUNT.your_photo')
									}}</span>
									<span class="text-sm font-normal text-grey-500">
										{{ $t('ACCOUNT.photo_appears_profile') }}</span
									>
								</div>
								<div class="flex basis-3/6">
									<div class="flex">
										<CollaboratorAvatar
											data-cy="upload-image"
											class="!h-[64px] !w-[64px] !cursor-default"
											:src="imageUrl"
										/>
									</div>

									<div
										v-if="isInternalUser"
										class="upload-style ml-5 flex w-full items-center justify-center"
										:class="{ 'cursor-not-allowed bg-grey-50': !isInternalUser }"
									>
										<a-upload
											v-model:file-list="fileList"
											name="avatar"
											class="light-border relative cursor-pointer"
											:class="{ 'cursor-not-allowed': !isInternalUser }"
											:show-upload-list="false"
											:before-upload="beforeUpload"
											@change="handleChangePicture"
										>
											<div>
												<div class="flex flex-col items-center justify-center py-4">
													<div class="flex items-center justify-center rounded-3xl bg-grey-50 px-2 py-2">
														<div
															class="flex h-[32px] w-[32px] items-center justify-center rounded-3xl bg-grey-100 px-2 py-2"
														>
															<div v-if="isInternalUser">
																<img src="~/assets/icons/upload/upload_cloud_black.svg?componentText" />
															</div>
															<div v-else>
																<img src="~/assets/icons/upload/upload_cloud.svg?componentText" />
															</div>
														</div>
													</div>

													<span
														class="upload-click-text pt-3 text-center"
														:class="{ '!text-grey-300': !isInternalUser }"
													>
														{{ $t('ACCOUNT.click_to_select') }}
													</span>

													<span class="cursor-default text-center text-sm font-normal text-grey-500">
														{{ $t('ACCOUNT.or_drag_and_drop_image') }}
													</span>
												</div>
											</div>
										</a-upload>
									</div>
									<div
										v-else
										class="upload-style ml-5 flex w-full items-center justify-center"
										:class="{ 'cursor-not-allowed bg-grey-50': !isInternalUser }"
									>
										<div>
											<div class="flex flex-col items-center justify-center py-4">
												<div class="flex items-center justify-center rounded-3xl bg-grey-50 px-2 py-2">
													<div
														class="flex h-[32px] w-[32px] items-center justify-center rounded-3xl bg-grey-100 px-2 py-2"
													>
														<div v-if="!isInternalUser">
															<img src="~/assets/icons/upload/upload_cloud_black.svg" />
														</div>
														<div v-else>
															<img src="~/assets/icons/upload/upload_cloud.svg" />
														</div>
													</div>
												</div>

												<span
													class="upload-click-text pt-3"
													:class="{ '!text-grey-300': !isInternalUser }"
												>
													{{ $t('ACCOUNT.click_to_select') }}
												</span>

												<span class="cursor-default text-center text-sm font-normal text-grey-500">
													{{ $t('ACCOUNT.or_drag_and_drop_image') }}
												</span>
											</div>
										</div>
									</div>
								</div>
								<div
									id="div fantasma para dar espaço"
									class="basis-1/6"
								></div>
							</div>
							<div class="flex pb-5 pt-6 px-4">
								<div class="basis-2/6">
									<span class="block text-sm font-medium text-grey-700">{{
										$t('GLOBAL.password')
									}}</span>
									<span class="text-sm font-normal text-grey-500">{{
										$t('ACCOUNT.password_update')
									}}</span>
								</div>
							</div>
							<div class="flex pt-4 px-4">
								<div class="basis-2/6">
									<span class="text-sm font-medium text-grey-700">{{
										$t('ACCOUNT.current_password')
									}}</span>
								</div>

								<a-form-item
									name="password"
									class="!mb-0 basis-3/6"
								>
									<a-input
										v-model:value="profileFormState.oldPassword"
										:placeholder="$t('ACCOUNT.enter_current_password')"
										:disabled="!isInternalUser"
										:type="inputTypeCurrentPassword"
										class="py-1.5"
									>
										<template #suffix>
											<DisplayPasswordButton @change="inputTypeCurrentPassword = $event" />
										</template>
									</a-input>
								</a-form-item>

								<div
									id="div fantasma para dar espaço"
									class="basis-1/6"
								></div>
							</div>
							<div class="flex pt-5 px-4">
								<div class="basis-2/6">
									<span class="text-sm font-medium text-grey-700">{{
										$t('ACCOUNT.new_password')
									}}</span>
								</div>

								<a-form-item
									name="newPassword"
									class="!mb-0 basis-3/6"
								>
									<a-input
										v-model:value="profileFormState.newPassword"
										:placeholder="$t('ACCOUNT.enter_new_password')"
										class="basis-3/6 py-1.5"
										:disabled="!isInternalUser"
										:type="inputTypeNewPassword"
										@change="handleNewPasswordChange"
									>
										<template #suffix>
											<DisplayPasswordButton @change="inputTypeNewPassword = $event" />
										</template>
									</a-input>
								</a-form-item>

								<div
									id="div fantasma para dar espaço"
									class="basis-1/6"
								></div>
							</div>
							<div class="flex pl-2 pt-1.5">
								<div
									id="div fantasma para dar espaço"
									class="basis-2/6"
								></div>
								<PasswordStrength
									ref="passwordStrengthRef"
									:password="profileFormState.newPassword"
									:rules="passwordRules"
									class="basis-3/6"
									@update="validPassword = $event"
								/>
								<div
									id="div fantasma para dar espaço"
									class="basis-1/6"
								></div>
							</div>
							<div class="light-border-b flex pb-4 pt-5 px-4">
								<div class="basis-2/6">
									<span class="text-sm font-medium text-grey-700">{{
										$t('ACCOUNT.confirm_new_password')
									}}</span>
								</div>

								<a-form-item
									name="confirmePassword"
									class="!mb-0 basis-3/6"
									v-bind="validateInfos.confirmPassword"
								>
									<a-input
										v-model:value="profileFormState.confirmPassword"
										:placeholder="$t('ACCOUNT.re_enter_new_password')"
										class="py-1.5"
										:disabled="!isInternalUser"
										:type="inputTypeConfirmedPassword"
									>
										<template #suffix>
											<DisplayPasswordButton @change="inputTypeConfirmedPassword = $event" />
										</template>
									</a-input>
								</a-form-item>
								<div
									id="div fantasma para dar espaço"
									class="basis-1/6"
								></div>
							</div>
						</a-form>
						<div
							v-if="useSuperAIStore().superAIConfig?.freeSuperIaRequestCount"
							class="light-border-b px-4 basis-2/6 pb-5 pt-4 gap-[20px] flex flex-col"
						>
							<FreeCreditsMenu class="w-[384px]" />
						</div>
						<div
							v-else-if="!useWhiteLabel().isWhiteLabelApp.value"
							id="ai-key-section"
							class="light-border-b px-4 basis-2/6 pb-5 pt-4 gap-[20px] flex flex-col"
						>
							<div class="flex flex-col">
								<span class="block text-sm font-medium text-grey-900 mb-2">{{
									$t('GLOBAL.setup_key_of', {
										name: useAiChatWithYourDataStore().getAIName
									})
								}}</span>
								<span class="text-sm font-normal text-grey-500">
									{{ $t('AI.config_key_tip_1') }}</span
								>
								<span class="text-sm font-normal text-grey-500">
									{{
										$t('AI.config_key_tip_2', {
											name: useAiChatWithYourDataStore().getAIName
										})
									}}</span
								>
								<div class="flex items-center gap-2">
									<span class="font-bold text-sm text-grey-500">
										{{ $t('AI.requirements_tier_1') }}
									</span>
								</div>
							</div>

							<div class="flex gap-4 flex-col">
								<div class="flex items-center gap-2">
									<a-input
										v-model:value="localAIKey"
										class="h-[44px] max-w-[500px]"
										:disabled="connectingAIKey"
										allow-clear
										:placeholder="$t('AI.enter_your_key')"
										@change="clearKey"
									>
									</a-input>
									<MitraButton
										:text="$t('AI.validate_key')"
										color="var(--violet-600)"
										:loading="connectingAIKey"
										@click="setAIKey"
									/>
								</div>
							</div>
							<div class="flex items-center w-full">
								<span
									v-if="isDeveloperWorkspace"
									class="text-sm font-medium text-violet-700 noselect cursor-pointer pr-4 light-border-r"
									@click="useDialogStore().switchDialog('aiInfoController', true)"
									>{{ $t('AI.how_works') }}</span
								>
								<span
									class="text-sm font-medium text-violet-700 noselect cursor-pointer pl-4"
									@click="useDialogStore().switchDialog('aiKeyController', true)"
									>{{ $t('AI.how_get_key') }}</span
								>
							</div>
						</div>
						<div class="basis-2/6 pb-5 pt-4 px-4">
							<span class="block text-sm font-medium text-grey-700">{{
								$t('ACCOUNT.language')
							}}</span>
							<span class="text-sm font-normal text-grey-500">
								{{ $t('ACCOUNT.language_config_body') }}</span
							>
						</div>
						<div class="light-border-b flex items-center justify-center pb-6 pt-4 px-4">
							<div class="basis-2/6">
								<span class="text-sm font-medium text-grey-700">{{
									$t('ACCOUNT.language')
								}}</span>
							</div>
							<LanguageSettings
								class="basis-3/6"
								:is-user-settings="true"
							/>
							<div
								id="div fantasma para dar espaço"
								class="basis-1/6"
							></div>
						</div>

						<div class="h-[40px] w-[160px] pt-6 px-4">
							<MitraButton
								:text="$t('ACCOUNT.logout')"
								text-color="var(--violet-600)"
								:icon="mdiExitToApp"
								class="!bg-grey-50"
								outlined
								@click="handleExit"
							/>
						</div>

						<!-- Fim da configurações -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { storeToRefs } from 'pinia';
	import { mdiExitToApp, mdiCheckCircleOutline } from '@mdi/js';
	import { type UploadFile, Form } from 'ant-design-vue';
	import type { UploadChangeParam } from 'ant-design-vue/es/upload/interface';
	import toastNotify from '~~/helpers/toast_notify';
	import { GLOBALS } from '~/helpers/contants/global_constants';

	const { passwordRules } = usePasswordRules();
	const { isDeveloperWorkspace } = storeToRefs(useWorkspaceStore());

	const props = defineProps({
		clearProject: {
			type: Boolean,
			default: false
		}
	});

	interface FormState {
		name: string;
		oldPassword: string;
		newPassword: string;
		confirmPassword: string;
		// email: string;
	}

	const inputTypeCurrentPassword = ref<InputPasswordType>('password');
	const inputTypeNewPassword = ref<InputPasswordType>('password');
	const inputTypeConfirmedPassword = ref<InputPasswordType>('password');

	const isInternalUser = computed(() => {
		if (loggedUser.value) {
			return loggedUser.value.userOrigin === 'internal_user';
		}
	});

	const hasSomeChange = computed(() => {
		return nameOrPictureChange.value || passwordChange.value;
	});

	const nameOrPictureChange = computed(() => {
		return (
			profileFormState.value.name !== '' &&
			profileFormState.value.name !== loggedUser.value?.name
		);
	});
	const passwordChange = computed(() => {
		return (
			profileFormState.value.confirmPassword === profileFormState.value.newPassword &&
			profileFormState.value.newPassword !== '' &&
			profileFormState.value.oldPassword !== '' &&
			validPassword.value
		);
	});
	const validPassword = ref(false);
	const connectingAIKey = ref(false);

	const localAIKey = ref('');

	const profileFormState = ref<FormState>({
		name: '',
		oldPassword: '',
		newPassword: '',
		confirmPassword: ''
		// email: ''
	});

	props.clearProject && useWorkspaceStore().setEmptyProject();
	const aiStore = useAiChatWithYourDataStore();
	aiStore.setLangflowThreadID('');
	aiStore.resetLangflowChat();

	if (useAppStore().copilotAIChat) {
		useAppStore().toggleAIChatSidebar();
	}

	useScrollManager({
		elementId: 'ai-key-section'
	});

	const useStore = useUserStore();
	const { loggedUser } = storeToRefs(useStore);
	const { fileList, handleChange, imageUrl, lessThan2MB, isJpgOrPng } =
		useImgUpload();

	localAIKey.value =
		useSuperAIStore().getKeyByModelId(GLOBALS.AI_MODELS.GEMINI.id) ?? '';

	const formStateRulesRef = reactive({
		name: [
			{
				...required,
				message: useNuxtApp().$translate('ACCOUNT.field_cannot_empty')
			}
		]
		// currentPassword: [
		// 	{
		// 		...required,
		// 		message: useNuxtApp().$translate('LOGIN.please_insert_email')
		// 	}
		// ],
		// newPassword: [
		// 	{
		// 		...required,
		// 		message: useNuxtApp().$translate('LOGIN.please_insert_password')
		// 	}
		// ],
		// confirmPassword: [
		// 	{
		// 		...required,
		// 		message: useNuxtApp().$translate('LOGIN.please_insert_password')
		// 	}
		// ]
	});

	const useForm = Form.useForm;

	const { validateInfos } = useForm(profileFormState, formStateRulesRef);

	// function onSubmit() {
	// 	validate()
	// 		.then(() => {
	// 			onFinish(formState);
	// 		})
	// 		.catch(() => {
	// 			toastNotify('LOGIN.fill_required_fields');
	// 		});
	// }

	async function setAIKey() {
		connectingAIKey.value = true;
		const data = await useSuperAIStore().verifyGeminiKey(localAIKey.value);
		if (data?.tier1) {
			await updateKey();
		} else {
			connectingAIKey.value = false;
			toastNotify(`AI.key_doesnt_fit`);
		}
	}

	async function updateKey() {
		try {
			await useSuperAIStore().updateSuperAIConfig({
				modelId: GLOBALS.AI_MODELS.GEMINI.id,
				modelKey: localAIKey.value
			});
			toastNotify(`AI.${localAIKey.value.length ? 'key_connected' : 'key_removed'}`, {
				icon: mdiCheckCircleOutline
			});
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error(error);
		} finally {
			connectingAIKey.value = false;
		}
	}

	function clearKey() {
		if (!localAIKey.value.length) {
			connectingAIKey.value = true;
			updateKey();
		}
	}

	async function handleChangePicture(info: UploadChangeParam<UploadFile<any>>) {
		const pictureData = new FormData();
		const file = info.file.originFileObj;
		if (file) {
			pictureData.append('file', new Blob([file], { type: file.type }));
		}

		if (loggedUser.value) {
			if (info.file.status === 'done') {
				try {
					await useStore.updateUserPicture(loggedUser.value.id, pictureData);
					toastNotify('TOAST.profile_photo_successfully_changed', {
						icon: mdiCheckCircleOutline
					});
				} catch (error) {
					toastNotify('TOAST.fail_change_profile_picture');
				}
			}
			handleChange(info);
		}
	}

	onMounted(() => {
		setProfileData();
	});

	// function openExternalLink(url: string) {
	// 	window.open(url, '_blank');
	// }

	const setProfileData = () => {
		if (loggedUser.value) {
			imageUrl.value = loggedUser.value.picture ?? '';
			profileFormState.value.name = loggedUser.value.name ?? '';
			// profileFormState.value.email = loggedUser.value.email ?? '';
		}
	};

	const passwordStrengthRef = ref(false);

	function handleNewPasswordChange() {
		if (profileFormState.value.newPassword === '') {
			passwordStrengthRef.value = false;
		} else {
			passwordStrengthRef.value = true;
		}
	}

	function disposePassword() {
		profileFormState.value.oldPassword = '';
		profileFormState.value.newPassword = '';
		profileFormState.value.confirmPassword = '';
		passwordStrengthRef.value = false;
	}

	async function handleSave() {
		if (loggedUser.value) {
			const newUser = loggedUser.value;
			if (nameOrPictureChange.value) {
				newUser.name = profileFormState.value.name;
				try {
					await useStore.updateUserData(newUser);
					toastNotify('TOAST.name_changed_successfully', {
						icon: mdiCheckCircleOutline
					});
				} catch (error) {
					toastNotify('TOAST.fail_change_name');
				}
			} else if (passwordChange.value) {
				newUser.currentPassword = profileFormState.value.oldPassword;
				newUser.password = profileFormState.value.newPassword;
				try {
					await useStore.updateUserPassword(newUser);
					toastNotify('TOAST.password_changed_successfully', {
						icon: mdiCheckCircleOutline
					});
					disposePassword();
				} catch (error) {
					toastNotify('TOAST.fail_change_password');
					disposePassword();
				}
			}
		}
	}

	function handleExit() {
		useLogout().logout();
	}

	const beforeUpload = (file: UploadFile) => {
		const validType = isJpgOrPng(file.type);
		const validSize = lessThan2MB(file.size);

		if (!validType) {
			toastNotify('Os formatos válidos são JPGE e PNG.');
		}

		if (!validSize) {
			toastNotify('A imagem deve ser menor que  2Mb');
		}

		return validType && validSize;
	};
</script>

<style lang="postcss" scoped>
	@import '~~/assets/css/shared/home.css';

	.input-padding {
		margin-right: 232px;
	}

	.upload-style {
		border: 1px solid #e7e8f0;
		border-radius: 8px;
	}

	.upload-click-text {
		color: #175cd3;
		font-weight: 500;
		font-size: 14px;
	}
	.card-margin {
		padding-top: 4px;
		padding-bottom: 4px;
	}

	:deep(.ant-input[disabled]) {
		background-color: var(--grey-50);
		color: var(--grey-500);
		font-weight: 400;
		font-size: 14px;
	}
</style>
