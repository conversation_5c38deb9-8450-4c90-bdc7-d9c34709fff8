<template>
	<div class="ai-credits-card w-full">
		<div class="card-header">
			<HugeiconsIcon
				:icon="FlashIcon"
				:size="20"
				:color="'var(--grey-700) !important'"
			/>
			<div class="text-sm font-medium text-grey-700 w-full">Créditos IA</div>
			<a-tooltip
				placement="top"
				overlay-class-name="base-tooltip"
			>
				<template #title>
					<span class="base-tooltip-text">
						{{ $t('SUPER_AI.free_credits') }}
					</span>
				</template>
				<HugeiconsIcon
					:icon="InformationCircleIcon"
					:size="20"
					:color="'var(--grey-700) !important'"
				/>
			</a-tooltip>
		</div>

		<!-- Detalhes dos Créditos Mensais -->
		<div class="credit-block">
			<div class="credit-row">
				<span class="text-xs font-normal text-grey-700">Créditos Cortesia</span>
				<span class="text-xs font-medium text-grey-700"
					>{{ monthlyUsed }}/{{ monthlyTotal }}</span
				>
			</div>
			<div class="progress-bar-free relative">
				<div
					class="progress-bar-free-fill"
					:style="{ width: progressPercentage }"
				></div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import {
		FlashIcon,
		InformationCircleIcon
	} from '@hugeicons-pro/core-stroke-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';

	const monthlyTotal = 5;

	const monthlyUsed = computed(() => {
		return (
			monthlyTotal - (useSuperAIStore().superAIConfig.freeSuperIaRequestCount || 0)
		);
	});
	const progressPercentage = computed(() => {
		return `${(monthlyUsed.value / monthlyTotal) * 100}%`;
	});
</script>

<style scoped lang="postcss">
	.ai-credits-card {
		border-radius: 0.75rem;
		border: 1px solid var(--grey-200);
		padding: 8px;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
			'Helvetica Neue', Arial, sans-serif;
		display: flex;
		flex-direction: column;
	}

	.card-header {
		display: flex;
		align-items: center;
		gap: 4px;
		padding: 4px 8px;
	}

	.credit-block {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
		padding: 6px 8px;
	}

	.credit-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 0.875rem;
	}

	.progress-bar-free {
		width: 100%;
		height: 6px;
		background-color: var(--grey-200);
		border-radius: 9999px;
		overflow: hidden;
	}

	.progress-bar-free-fill {
		height: 100%;
		background-color: var(--violet-600);
		border-radius: 9999px;
		transition: width 0.4s ease-in-out;
	}
</style>
