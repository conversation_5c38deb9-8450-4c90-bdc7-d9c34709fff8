<template>
	<div
		class="toogle"
		@click="toggleView"
	>
		<div
			class="border-right flex p-1 items-center justify-center"
			:style="{
				color: !isShowingCode ? 'var(--violet-700)' : 'var(--grey-500)',
				backgroundColor: !isShowingCode ? 'var(--violet-50)' : 'white'
			}"
		>
			<HugeiconsIcon
				:icon="ViewIcon"
				:size="20"
				:color="!isShowingCode ? 'var(--violet-700)' : 'var(--grey-500)'"
			/>
		</div>
		<div
			class="flex p-1 items-center justify-center"
			:style="{
				color: isShowingCode ? 'var(--violet-700)' : 'var(--grey-500)',
				backgroundColor: isShowingCode ? 'var(--violet-50)' : 'white'
			}"
		>
			<HugeiconsIcon
				:icon="SourceCodeIcon"
				:size="20"
				:color="isShowingCode ? 'var(--violet-700)' : 'var(--grey-500)'"
			/>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { SourceCodeIcon, ViewIcon } from '@hugeicons-pro/core-stroke-rounded';

	const props = defineProps({
		messageId: {
			type: String,
			required: true
		},
		isShowingCode: {
			type: Boolean,
			required: true
		}
	});

	const emit = defineEmits(['toggle']);

	function toggleView() {
		emit('toggle', props.messageId);
	}
</script>

<style scoped lang="postcss">
	.toogle {
		height: 28px;
		border-radius: 8px;
		border: 1px solid var(--gray-300, #d0d5dd);
		display: flex;
		overflow: hidden;
		cursor: pointer;
		font-size: 14px;
		font-family: Inter;
		font-style: normal;
		font-weight: 500;
		line-height: 20px;
	}
	.border-right {
		border-right: 1px solid var(--gray-300, #d0d5dd);
	}
</style>
