/* Garantir que o header do widget fique sempre visível */
.iframe-wrapper {
	position: relative;
	z-index: 1;
	overflow: visible !important;
}

.iframe-wrapper .light-border-b {
	position: relative;
	z-index: 10;
	background: white;
	border-bottom: 1px solid #e5e7eb;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.iframe-wrapper iframe {
	position: relative;
	z-index: 1;
}

/* Garantir que o IframeScreenComponent não sobreponha o header */
.iframe-wrapper .h-full {
	height: calc(100% - 10px) !important;
}

/* Removi no contexto da IA */
.iframe-chat-ia .h-full {
	height: calc(100%) !important;
}

/* Forçar visibilidade dos controles */
.iframe-wrapper .flex.items-center.justify-between {
	display: flex !important;
	visibility: visible !important;
	opacity: 1 !important;
}

/* Drag & Drop Styles */
.ai-input-box {
	position: relative;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-input-box.drag-over {
	box-shadow: none !important;
}

.drag-active {
	border-radius: 18px;
	border: 2px dashed var(--violet-400) !important;
	/* background: rgba(236, 233, 254, 0.5);
		backdrop-filter: blur(5px); */
}

.ai-input-box.drag-active {
	/* border-color: var(--violet-600);
		box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
		transform: scale(1.02); */
}

/* Drag and Drop Styles for main wrapper */
/* .studio-mode-wrapper.drag-over {
		background-color: rgba(59, 130, 246, 0.03);
		border: 2px dashed #3b82f6 !important;
		border-radius: 12px;
	}

	.studio-mode-wrapper.drag-active {
		background-color: rgba(139, 92, 246, 0.03);
		border: 2px dashed #8b5cf6 !important;
		border-radius: 12px;
	} */

/* Enhanced drop zone overlay for full component */
.studio-mode-wrapper.drag-active::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		135deg,
		rgba(59, 130, 246, 0.05) 0%,
		rgba(139, 92, 246, 0.05) 100%
	);
	border-radius: 12px;
	z-index: 1;
	pointer-events: none;
}

/* Uploaded Files Display Styles */
.uploaded-files-display {
	margin-bottom: 12px;
}

.uploaded-files-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
	gap: 12px;
}

.uploaded-file-item {
	position: relative;
	border-radius: 8px;
	overflow: hidden;
	background: var(--grey-50);
	border: 1px solid var(--grey-200);
	transition: all 0.2s ease;
}

.uploaded-file-item.uploaded-file-loading {
	border-color: var(--violet-300);
	background: var(--violet-50);
}

/* Loading overlay for uploading files */
.uploaded-file-loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 10;
	backdrop-filter: blur(2px);
}

.uploaded-file-spinner {
	margin-bottom: 8px;
}

.uploaded-file-progress {
	display: flex;
	align-items: center;
	gap: 8px;
}

.uploaded-file-progress-text {
	font-size: 12px;
	font-weight: 600;
	color: var(--violet-700);
}

/* Image Preview Styles */
.uploaded-image-preview {
	position: relative;
	width: 100%;
	height: 120px;
}

.uploaded-file-image {
	width: 200px;
	height: 132px;
}

.uploaded-file-document {
	width: 320px;
	height: 58px;
	border-radius: 8px;
	border: 1px solid var(--grey-300);
	background: var(--white);
	display: flex;
	flex-shrink: 0;
	padding: 9px;
	align-items: center;
	gap: 8px;
	&-small {
		width: 250px !important;
	}
}

.uploaded-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	cursor: pointer;
	transition: transform 0.2s ease;
}

/* Placeholder for loading images */
.uploaded-image-placeholder {
	width: 100%;
	height: 120px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: var(--grey-100);
	border: 2px dashed var(--grey-300);
}

/* Error state styles */
.uploaded-file-error {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: var(--red-600);
	color: white;
	padding: 4px 8px;
	z-index: 5;
}

.uploaded-file-error-text {
	font-size: 11px;
	font-weight: 500;
}

/* Spin animation for loading icons */
.animate-spin {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.uploaded-file-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
	padding: 8px;
	color: white;
}

.uploaded-file-info {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.uploaded-file-name {
	font-size: 12px;
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.uploaded-file-size {
	font-size: 10px;
	opacity: 0.8;
}

/* Document Preview Styles */
.uploaded-document-preview {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	min-height: 60px;
}

.uploaded-document-icon {
	flex-shrink: 0;
}

.uploaded-file-download {
	flex-shrink: 0;
	padding: 4px;
	border-radius: 4px;
	background: var(--violet-50);
	transition: background-color 0.2s ease;
}

.uploaded-file-download:hover {
	background: var(--violet-100);
}

/* User message container */
.user-message-container {
	width: 100%;
}

/* File Preview Area Styles */
.file-preview-area {
	position: relative;
	width: 100%;
}

/* File Preview Summary (when input not focused) */
.file-preview-summary {
	padding: 12px 16px;
	background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
	border: 1px solid #e2e8f0;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.2s ease;
}

.file-preview-summary:hover {
	background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
	border-color: var(--violet-300);
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(139, 92, 246, 0.1);
}

.file-preview-summary-content {
	display: flex;
	align-items: center;
	gap: 8px;
}

.file-preview-summary-text {
	font-size: 14px;
	font-weight: 500;
	color: var(--grey-700);
}

.file-preview-summary-hint {
	font-size: 12px;
	color: var(--grey-500);
	margin-left: auto;
}

/* File Preview Gallery (when user clicks to view details) */
.file-preview-gallery {
	width: 100%;
}

.file-preview-gallery-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
	padding: 8px 12px;
	background: var(--grey-50);
	border: 1px solid var(--grey-200);
	border-radius: 6px;
}

.file-preview-gallery-title {
	font-size: 14px;
	font-weight: 500;
	color: var(--grey-700);
}

.file-preview-collapse-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	background: none;
	border: none;
	cursor: pointer;
	padding: 4px 8px;
	border-radius: 4px;
	transition: all 0.2s ease;
	font-size: 12px;
	color: var(--grey-600);
}

.file-preview-collapse-btn:hover {
	background: var(--grey-200);
	color: var(--grey-800);
}

.file-preview-gallery-grid {
	width: 100%;
}

.file-preview-grid-container {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
	gap: 12px;
	width: 100%;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
	.file-preview-grid-container {
		grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
		gap: 8px;
	}
}

@media (max-width: 480px) {
	.file-preview-grid-container {
		grid-template-columns: 1fr;
		gap: 8px;
	}
}

/* File Preview Card */
.file-preview-card {
	position: relative;
	border-radius: 8px;
	/* overflow: hidden;
		background: var(--grey-50);
		border: 1px solid var(--grey-200);
		transition: all 0.2s ease; */
}

.file-preview-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-preview-card.file-preview-document {
	display: flex;
	gap: 8px;
	border: 1px solid var(--grey-300);
	border-radius: 8px;
	transition: all 0.2s ease;
	height: 100%;
	padding: 9px;
}

/* Enhanced Transition Animations */
.file-preview-transition-enter-active {
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-preview-transition-leave-active {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.file-preview-transition-enter-from {
	opacity: 0;
	transform: translateY(-10px) scale(0.95);
}

.file-preview-transition-leave-to {
	opacity: 0;
	transform: translateY(10px) scale(0.95);
}

/* File Preview Slide Animation (for individual cards) */
.file-preview-slide-enter-active {
	transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-preview-slide-leave-active {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.file-preview-slide-enter-from {
	opacity: 0;
	transform: translateY(20px) scale(0.9);
}

.file-preview-slide-leave-to {
	opacity: 0;
	transform: translateY(-20px) scale(0.9);
}

.file-preview-slide-move {
	transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Image styles within unified container */
.file-preview-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	transition: transform 0.2s ease;
	border-radius: 8px;
}

.file-preview-image:hover {
	transform: scale(1.05);
}

/* Document icon styles within unified container */
.file-preview-document-icon {
	display: flex;
	width: 40px;
	height: 40px;
	padding: 0px 12px;
	justify-content: center;
	align-items: center;
	gap: 10px;
	flex-shrink: 0;
	border-radius: 8px;
	background: var(--primary-500);
	&.icon--pdf {
		background: var(--pink-500);
	}
	&.icon--csv {
		background: var(--success-500);
	}
	&.icon--txt {
		background: var(--primary-500);
	}
	&.icon--doc {
		background: var(--primary-500);
	}
}

.file-preview-media-container:hover .file-preview-document-icon {
	transform: scale(1.1);
}

/* File Preview Container Styles */
.file-preview-container {
	display: flex;
	flex-direction: column;
	gap: 8px;
	padding: 12px;
	background: var(--grey-50);
	border: 1px solid var(--grey-200);
	border-radius: 8px;
	transition: all 0.2s ease;
	position: relative;
	height: 100%;
}

.file-preview-container:hover {
	background: var(--grey-100);
	border-color: var(--violet-300);
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(139, 92, 246, 0.1);
}

.file-preview-info {
	flex: 1;
	min-width: 0;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 8px;
}

.file-preview-details {
	flex: 1;
	min-width: 0;
}

.file-preview-name {
	font-size: 13px;
	font-weight: 500;
	color: var(--grey-900);
	margin: 0 0 4px 0;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	line-height: 1.2;
}

.file-preview-size {
	font-size: 11px;
	color: var(--grey-600);
	margin: 0 0 2px 0;
}

.file-preview-type {
	font-size: 10px;
	color: var(--grey-500);
	text-transform: uppercase;
	font-weight: 600;
	margin: 0;
}

.file-preview-remove {
	background: none;
	border: none;
	cursor: pointer;
	padding: 4px;
	border-radius: 4px;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.file-preview-remove:hover {
	background: var(--grey-200);
	transform: scale(1.1);
}

.file-preview-remove:active {
	transform: scale(0.95);
}

/* File Preview Overlay */
.file-preview-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.2s ease;
	cursor: pointer;
	border-radius: 6px;
}

.file-preview-media-container:hover .file-preview-overlay {
	opacity: 1;
}

.drag-drop-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;

	border-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 50;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border-radius: 18px;
	background: var(--drop-zone-bg);
	backdrop-filter: blur(5px);
}

/* Dual Drop Zone Colors */
.drag-drop-overlay.drag-drop-image {
	background: rgba(59, 130, 246, 0.95); /* Blue for images */
}

.drag-drop-overlay.drag-drop-document {
	background: rgba(139, 92, 246, 0.95); /* Purple for documents */
}

.drag-drop-overlay.drag-drop-mixed {
	background: linear-gradient(
		135deg,
		rgba(59, 130, 246, 0.95) 0%,
		rgba(139, 92, 246, 0.95) 100%
	); /* Gradient for mixed */
}

/* Fallback for unknown file types
	.drag-drop-overlay:not(.drag-drop-image):not(.drag-drop-document):not(
			.drag-drop-mixed
		) {
		background: rgba(107, 114, 128, 0.95); /* Gray for unknown 
	} 
	*/

.drag-drop-content {
	text-align: center;
	color: white;
}

/* .drag-drop-icon {
		margin: 0 auto 16px;
		animation: bounce 2s infinite;
	} */

.drag-drop-title {
	color: var(--violet-700);

	/* Text sm/Medium */
	font-family: Inter;
	font-size: 14px;
	font-style: normal;
	font-weight: 500;
	line-height: 20px; /* 142.857% */
}

.drag-drop-subtitle {
	font-size: 14px;
	opacity: 0.9;
	margin: 0;
}

/* Drag overlay animations */
.drag-overlay-enter-active,
.drag-overlay-leave-active {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drag-overlay-enter-from,
.drag-overlay-leave-to {
	opacity: 0;
	transform: scale(0.95);
}

/* Enhanced File Preview Styles */
.file-preview-enhanced {
	margin-bottom: 16px;
	animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-preview-container {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
	border: 1px solid #e2e8f0;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
	transition: all 0.2s ease;
	min-width: 280px;
	max-width: 100%;
	width: 100%;
}

.file-preview-container:hover {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
	transform: translateY(-1px);
}

.file-preview-image-wrapper {
	position: relative;
	flex-shrink: 0;
}

.file-preview-image {
	/* width: 48px;
		height: 48px;
		object-fit: cover;
		border-radius: 8px;
		border: 2px solid white;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
}

/* File type specific styling */
.file-preview-enhanced.file-preview-image .file-preview-container {
	border-left: 4px solid #3b82f6; /* Blue accent for images */
}

.file-preview-enhanced.file-preview-document .file-preview-container {
	border-left: 4px solid #8b5cf6; /* Purple accent for documents */
}

.file-preview-overlay {
	position: absolute;
	top: -4px;
	right: -4px;
	width: 20px;
	height: 20px;
	background: var(--violet-600);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2px solid white;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-preview-info {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-width: 0;
}

.file-preview-details {
	flex: 1;
	min-width: 0;
}

.file-preview-name {
	font-size: 14px;
	font-weight: 500;
	color: #1e293b;
	margin: 0 0 2px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.file-preview-size {
	font-size: 12px;
	color: #64748b;
	margin: 0;
}

.file-preview-type {
	font-size: 11px;
	color: #94a3b8;
	margin: 2px 0 0;
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.file-preview-remove {
	flex-shrink: 0;
	width: 32px;
	height: 32px;
	border: none;
	background: #f1f5f9;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #64748b;
	cursor: pointer;
	transition: all 0.2s ease;
}

.file-preview-remove:hover {
	background: #fee2e2;
	color: #dc2626;
	transform: scale(1.05);
}

/* File preview slide animation */
.file-preview-slide-enter-active,
.file-preview-slide-leave-active {
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-preview-slide-enter-from {
	opacity: 0;
	transform: translateY(-20px) scale(0.95);
}

.file-preview-slide-leave-to {
	opacity: 0;
	transform: translateY(-10px) scale(0.98);
}

/* Slide up animation */
@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Google Search Icon Styles */
.google-icon {
	font-family: 'Google Sans', 'Roboto', sans-serif;
	font-weight: 700;
	font-size: 14px;
	line-height: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 18px;
	height: 18px;
}

.search-grounding-btn:hover {
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
