<template>
	<div
		v-if="showModal"
		class="flex flex-col justify-center items-center w-full absolute bg-white h-full"
	>
		<div class="flex flex-col justify-start items-center max-w-[720px] z-[2]">
			<AILogo
				v-if="!useAppStore().playgroundMode && !isSankhyaClient"
				width="40"
				height="24"
				image-name="lilaGradientHead"
				:white-label-props="{ height: '24' }"
			/>
			<div class="text-grey-900 text-base font-medium mt-[16px] mb-[8px]">
				{{ `${$t('AI.start_experience_lila')} ${getAIName}` }}
			</div>
			<div class="text-grey-700 text-sm font-normal text-center mb-[16px] w-[90%]">
				{{ $t('AI.for_ai_work') }}
			</div>
			<div class="flex gap-3">
				<MitraButton
					:text="$t('AI.execute_function')"
					color="var(--violet-600)"
					@click="modalController = true"
				/>
				<MitraButton
					:huge-icon="Copy01Icon"
					:loading="loadingData"
					color="var(--grey-700)"
					outlined
					text-color="var(--grey-700)"
					class="!min-w-[40px] !min-h-[40px]"
					@click="copyData"
				/>
			</div>
		</div>
		<div class="left-bubble absolute"></div>
		<div class="right-bubble absolute"></div>

		<BaseNoticeModal
			v-model:modalController="modalController"
			:main-icon="mdiAlertCircleOutline"
			:cancel-props="{
				text: 'Deixar para depois',
				loading: false
			}"
			:confirm-props="{
				text: 'Sim, preparar agora',
				loading: loading
			}"
			default-buttons
			:main-text="'Tem certeza de que deseja executar a função?'"
			:sub-text="`Essa ação é obrigatória para usar a Lila, mas pode impactar alguns objetos de banco, que talvez precisem de ajustes depois. Recomendamos que você siga apenas se estiver disponível para revisar e corrigir os dados, se necessário. Sugerimos que faça um backup do seu projeto antes de continuar.`"
			@cancel="modalController = false"
			@confirm="updateUsages"
		>
		</BaseNoticeModal>
	</div>
</template>
<script lang="ts" setup>
	import { Copy01Icon } from '@hugeicons-pro/core-stroke-rounded';
	import { mdiCheckCircleOutline, mdiAlertCircleOutline } from '@mdi/js';
	import toastNotify from '~~/helpers/toast_notify';

	const emit = defineEmits(['update-menu']);
	const { isSankhyaClient } = useWhiteLabel();
	const aiChatWithYourDataStore = useAiChatWithYourDataStore();
	const { getAIName } = storeToRefs(aiChatWithYourDataStore);

	const loading = ref(false);
	const loadingData = ref(false);
	const modalController = ref(false);
	const dataContent = ref();

	checkUpdate();

	const showModal = ref(false);

	async function checkUpdate() {
		const { data, error } = await useAiService().isAllCadViewsUpdatable();
		if (!error) {
			showModal.value = !data;
			emit('update-menu', !showModal.value);
		}
		const { data: content } = await useAiService().usageOfNonsUpdatable();
		dataContent.value = content;
	}

	async function updateUsages() {
		loading.value = true;
		await useAiService().makeAllUpdatable();
		loading.value = false;
		checkUpdate();
	}

	function copyData() {
		navigator.clipboard.writeText(JSON.stringify(dataContent.value));
		toastNotify(`Data copied`, {
			icon: mdiCheckCircleOutline
		});
	}
</script>
<style scoped lang="postcss">
	.left-bubble,
	.right-bubble {
		position: absolute;
		z-index: 1;
		border-radius: 50%;
		filter: blur(100px);
		font-size: 0;
		color: transparent;
		transition-duration: 0.5s;
		transition-timing-function: ease-in-out;
	}
</style>
