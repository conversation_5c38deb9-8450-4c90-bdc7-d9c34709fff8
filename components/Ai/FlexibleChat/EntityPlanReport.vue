<template>
	<div class="container">
		<div class="header">
			<!-- <pre>{{ plan }}</pre> -->
			<span class="title">{{ $t(`LANGFLOW.plan.executed`) }}</span>
		</div>
		<div class="content">
			<a-collapse
				v-model:activeKey="activeKey"
				ghost
				expand-icon-position="right"
			>
				<a-collapse-panel
					v-for="(content, key) in reports"
					:key="'plan-' + key"
					:show-arrow="false"
				>
					<template #header>
						<div class="flex items-center justify-between gap-2">
							<span
								v-if="content.action !== 'RUN'"
								class="text-grey-900 text-sm font-normal leading-5 font-inter not-italic"
								>{{ $t(`LANGFLOW.plan.action.alt.${key}`) }}
							</span>
							<span
								v-else-if="content.action === 'RUN'"
								class="text-grey-900 text-sm font-normal leading-5 font-inter not-italic"
							>
								{{ $t(`LANGFLOW.plan.action.alt.${content.action}`) }}:</span
							>
							<!-- >{{ content.name }} que vou {{ getActionName(content.type) }}:</span
							> -->
							<span class="tag">{{ content.size }}</span>
						</div>
					</template>
					<template #extra>
						<HugeiconsIcon
							:icon="ArrowDown01Icon"
							:size="16"
						/>
					</template>
					<div class="flex flex-col gap-2">
						<div
							v-for="(item, index2) in (content as unknown as AIPlan).value"
							:key="'plan-item-' + index2"
							class="item-content flex items-center justify-between"
						>
							<div class="flex gap-2.5 items-center">
								<HugeiconsIcon
									:icon="TableIcon"
									:size="16"
									class="text-grey-500 shrink-0"
								/>
								<span
									:title="item.name ?? item.statement"
									class="text-grey-900 text-sm font-semibold uppercase leading-5 font-inter not-italic truncate max-w-[200px]"
									>{{ item.name ?? item.statement ?? 'Tabela' }}
								</span>
							</div>
							<a-tooltip
								placement="top"
								overlay-class-name="base-tooltip"
							>
								<template #title>
									<span class="base-tooltip-text">
										{{ $t('GLOBAL.soon') }}
									</span>
								</template>
								<HugeiconsIcon
									:icon="ArrowExpandIcon"
									:size="16"
									class="text-grey-500 shrink-0 cursor-not-allowed"
								/>
							</a-tooltip>
						</div>
					</div>
				</a-collapse-panel>
			</a-collapse>
		</div>
	</div>
</template>
<script setup lang="ts">
	import {
		ArrowDown01Icon,
		TableIcon,
		ArrowExpandIcon
	} from '@hugeicons-pro/core-stroke-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';

	defineProps<{
		reports: any[];
	}>();

	const activeKey = ref<string | string[]>([]);
</script>

<style lang="postcss" scoped>
	.container {
		border-radius: 16px;
		border: 1px solid var(--grey-200);
		background: var(--white);
		.header {
			@apply p-3 bg-grey-100 light-border-b gap-2.5 flex items-center justify-between;
			border-radius: 16px 16px 0px 0px;
		}
		.title {
			color: var(--grey-900);
			@apply text-sm font-bold leading-5 font-inter not-italic;
		}
		.tag {
			border-radius: 6px;
			border: 1px solid var(--grey-300);
			background: var(--white);
			height: 22px;
			width: 22px;
			display: flex;
			align-items: center;
			justify-content: center;
			> span {
				@apply text-grey-700 text-center text-xs font-medium leading-[18px] font-inter not-italic;
			}
		}

		.item-content {
			border-radius: 16px;
			border: 1px solid var(--grey-200);
			background: var(--white);
			padding: 12px;
		}
	}

	:deep(.ant-collapse-item-active .ant-collapse-extra) {
		svg {
			transform: rotate(-180deg) !important;
		}
	}
	:deep(
			.ant-collapse > .ant-collapse-item.ant-collapse-no-arrow > .ant-collapse-header
		) {
		padding-right: 12px;
	}

	:deep(
			.ant-collapse-ghost
				> .ant-collapse-item
				> .ant-collapse-content
				> .ant-collapse-content-box
		) {
		padding-top: 0px !important;
		/* padding-bottom: 0px !important; */
	}
</style>
