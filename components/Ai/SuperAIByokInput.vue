<template>
	<div
		class="flex flex-col items-center justify-center px-10 py-10"
		:style="{
			'--project-color': useWorkspaceStore().getProjectColor
		}"
	>
		<span class="text-lg font-medium text-grey-700">
			{{
				$t('AI.please_bring_your_own_key', {
					model: 'Gemini'
				})
			}}
		</span>

		<div
			v-if="!superAIBYOKModalData?.byWorkspace"
			class="flex flex-col gap-2"
		>
			<span class="text-md text-grey-600 mt-2">
				{{
					$t('AI.please_bring_your_own_key_tip', {
						aiName: useAiChatWithYourDataStore().getAIName,
						platform: useWhiteLabel().isSankhyaClient ? 'Analytics' : 'Mitra'
					}) +
					(!useWhiteLabel().isSankhyaClient
						? $t('AI.please_bring_your_own_key_tip_2')
						: '.')
				}}
				<a
					v-if="!useWhiteLabel().isSankhyaClient"
					class="link-text underline"
					@click="goToSettings"
					>{{ $t('AI.account_settings') }}</a
				>
			</span>

			<div class="flex items-center gap-2">
				<span class="font-bold text-sm text-grey-600">
					{{ $t('AI.requirements_tier_1') }}
				</span>
			</div>
		</div>

		<div
			v-else
			class="flex flex-col gap-2"
		>
			<span class="text-md text-grey-600 mt-2">
				{{
					$t('AI.please_bring_your_own_key_tip', {
						aiName: useAiChatWithYourDataStore().getAIName,
						platform: useWhiteLabel().isSankhyaClient ? 'Analytics' : 'Mitra'
					}) +
					(!useWhiteLabel().isSankhyaClient
						? $t('AI.please_bring_your_own_key_tip_2')
						: '.')
				}}
			</span>

			<div class="flex items-center gap-2">
				<span class="font-bold text-sm text-grey-600">
					{{ $t('AI.requirements_tier_1') }}
				</span>
			</div>
		</div>

		<div class="flex items-center justify-between mt-4 w-full gap-4">
			<a-input
				v-model:value="localAIKey"
				:style="{
					borderColor: isAssistantIdError ? 'red' : isNoCreditsError ? 'yellow' : '',
					backgroundColor: isAssistantIdError
						? 'rgba(255, 0, 0, 0.1)'
						: isNoCreditsError
						? 'rgba(255, 255, 0, 0.1)'
						: ''
				}"
				allow-clear
				class="h-[40px]"
				type="text"
				:class="{ playground: useAppStore().playgroundMode }"
				:disabled="loading"
				:placeholder="$t('AI.enter_your_key')"
				@change="clearKey"
				@press-enter="setAIKey"
			/>

			<MitraButton
				class="h-[40px]"
				loading-class="w-[45px] flex justify-center items-center"
				:text="$t('BUTTONS.save')"
				:loading="loading || validatingKey"
				:color="
					useAppStore().playgroundMode ? 'var(--project-color)' : 'var(--violet-600)'
				"
				@click="setAIKey"
			/>
		</div>
		<span
			v-if="isAssistantIdError"
			class="text-xs items-start text-red-300 w-full ml-5 mt-1"
		>
			{{ $t('TOAST.error_assistant_id_invalid_key') }}
		</span>
		<span
			v-if="isNoCreditsError"
			class="text-xs items-start text-yellow-300 w-full ml-5 mt-1"
		>
			{{ $t('TOAST.error_assistant_id_no_model') }}
		</span>
		<div class="flex items-center justify-center light-border-t mt-6 pt-4 w-full">
			<span
				v-if="
					!(
						superAIBYOKModalData?.byWorkspace ||
						!isDeveloperWorkspace ||
						useWhiteLabel().isWhiteLabelApp.value
					)
				"
				class="text-sm font-medium text-violet-700 noselect cursor-pointer pr-4 light-border-r"
				@click="useDialogStore().switchDialog('aiInfoController', true)"
				>{{ $t('AI.how_works') }}</span
			>
			<span
				class="text-sm font-medium text-violet-700 noselect cursor-pointer pl-4"
				@click="useDialogStore().switchDialog('aiKeyController', true)"
				>{{ $t('AI.how_get_key') }}</span
			>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { mdiCheckCircleOutline } from '@mdi/js';
	import { GLOBALS } from '~/helpers/contants/global_constants';
	import toastNotify from '~/helpers/toast_notify';
	import { ROUTE_PATHS } from '~~/helpers/route_paths';

	const localAIKey = ref('');
	const loading = ref(false);
	const validatingKey = ref(false);

	const isAssistantIdError = ref(null) as Ref<boolean | null>;
	const isNoCreditsError = ref(false);
	const { selectedWorkspace } = storeToRefs(useWorkspaceStore());
	const { superAIBYOKModalData } = storeToRefs(useDialogStore());
	const { isDeveloperWorkspace } = storeToRefs(useWorkspaceStore());
	if (!superAIBYOKModalData.value?.byWorkspace) {
		localAIKey.value =
			useSuperAIStore().getKeyByModelId(GLOBALS.AI_MODELS.GEMINI.id) ?? '';
	} else {
		const settings = useSuperAIStore().workspaceAISettings;
		localAIKey.value =
			!settings.fallbackAIKey && settings?.openaiAccessKey
				? settings.openaiAccessKey
				: '';
	}

	const emit = defineEmits(['close']);

	async function setAIKey() {
		validatingKey.value = true;
		const data = await useSuperAIStore().verifyGeminiKey(localAIKey.value);
		if (data?.tier1) {
			await updateKey();
		} else {
			validatingKey.value = false;
			toastNotify(`AI.key_doesnt_fit`);
		}
	}

	async function updateKey() {
		if (loading.value) return;
		loading.value = true;

		try {
			const isWorkspace = superAIBYOKModalData.value?.byWorkspace;
			const hasKey = !!localAIKey.value.length;
			const modelData = {
				modelId: GLOBALS.AI_MODELS.GEMINI.id,
				modelKey: localAIKey.value
			};

			if (isWorkspace) {
				const workspaceId = useWorkspaceStore().selectedWorkspace.id;
				const payload = {
					...modelData,
					workspaceId,
					enableChatWithYourData: true
				};

				await useSuperAIStore().createWorkspaceAIKey(payload);
			} else {
				await useSuperAIStore().updateSuperAIConfig(modelData);
			}

			toastNotify(`AI.${hasKey ? 'key_connected' : 'key_removed'}`, {
				icon: mdiCheckCircleOutline
			});

			setTimeout(() => emit('close'), 1000);
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error(error);
		} finally {
			loading.value = false;
		}
	}

	function clearKey() {
		if (!localAIKey.value.length) {
			updateKey();
		}
	}

	function goToSettings() {
		navigateTo(
			`/${ROUTE_PATHS.WORKSPACE_SHORTCUT}/${selectedWorkspace.value.id}${ROUTE_PATHS.USER_SETTINGS}`
		);
		emit('close');
	}

	// function findAssistant(data: any) {
	// 	return data?.data?.find(
	// 		(assistant: any) => assistant?.metadata?.createdByMitra === 'yes'
	// 	);
	// }

	// watch(
	// 	() => aiConfig?.value?.openaiAssistantId,
	// 	() => {
	// 		// recarregar pagina
	// 		// alert('A página será recarregada para aplicar as novas configurações.');
	// 		useAiChatWithYourDataStore().setToggleChangeOpenAIKeyAndAssistantID();
	// 	}
	// );
</script>

<style lang="postcss" scoped>
	.link-text {
		&:hover {
			color: var(--grey-800) !important;
			text-decoration: underline !important;
			font-weight: 500;
		}
	}
	.playground:hover,
	.playground:has(input:focus) {
		border-color: var(--project-color) !important;
		box-shadow: unset !important;
	}
</style>
