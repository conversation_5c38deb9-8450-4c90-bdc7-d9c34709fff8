<template>
	<div
		v-if="groundingMetadata"
		class="google-search-suggestions"
	>
		<div v-if="hasSearchSuggestions">
			<!-- Search Entry Point with rendered content -->
			<div
				v-if="searchEntryPoint?.renderedContent"
				class="search-entry-point"
				ref="searchEntryPointContainer"
			>
				<!-- Iframe será criado dinamicamente em JavaScript -->
			</div>

			<!-- Fallback: Manual rendering if renderedContent is not available -->
			<div
				v-if="false && searchQueries && searchQueries.length > 0"
				class="search-queries-fallback"
			>
				<h4 class="search-title">{{ $t('SUPER_AI.search_suggestions') }}</h4>
				<div class="search-queries">
					<div
						v-for="(query, index) in searchQueries"
						:key="index"
						class="search-query-item"
					>
						<a
							:href="`https://www.google.com/search?q=${encodeURIComponent(query)}`"
							target="_blank"
							rel="noopener noreferrer"
							class="search-query-link"
						>
							<svg
								class="search-icon"
								viewBox="0 0 24 24"
								width="16"
								height="16"
							>
								<path
									fill="currentColor"
									d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
								/>
							</svg>
							{{ query }}
						</a>
					</div>
				</div>
			</div>

			<!-- Web Search Results if available -->
			<div
				v-if="webSearchQueries && webSearchQueries.length > 0 && false"
				class="web-search-results"
			>
				<!-- <h4 class="search-title">{{ $t('SUPER_AI.web_search_results') }}</h4> -->
				<div class="web-results">
					<div
						v-for="(result, index) in webSearchQueries"
						:key="index"
						class="web-result-item"
					>
						<a
							v-if="result.uri"
							:href="result.uri"
							target="_blank"
							rel="noopener noreferrer"
							class="web-result-link"
						>
							<div class="web-result-title">{{ result.title || result.uri }}</div>
							<div
								v-if="result.snippet"
								class="web-result-snippet"
							>
								{{ result.snippet }}
							</div>
						</a>
						<div
							v-else
							class="web-result-no-link"
						>
							<div class="web-result-title">{{ result.title || 'Search Result' }}</div>
							<div
								v-if="result.snippet"
								class="web-result-snippet"
							>
								{{ result.snippet }}
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Grounding Supports with Collapse/Expand (only if we have groundingChunks with real URLs) -->
			<div
				v-if="processedSources && processedSources.length > 0"
				class="grounding-supports"
			>
				<!-- Collapsed State: Show summary -->
				<div
					v-if="!isExpanded"
					class="sources-summary"
					@click="toggleExpanded"
				>
					<div class="summary-content">
						<img
							:src="getFirstSourceFavicon()"
							:alt="getFirstSourceDomain()"
							class="summary-favicon"
							@error="(e) => handleFaviconError(e, processedSources[0])"
						/>
						<div class="summary-line">
							<span class="summary-domain">{{ getFirstSourceDomain() }}</span>
							<span class="summary-title">{{
								getFirstSourceTitle().replaceAll('*', '')
							}}</span>
						</div>
					</div>
				</div>

				<!-- Expanded State: Show all sources -->
				<transition
					name="expand"
					@enter="onEnter"
					@leave="onLeave"
				>
					<div
						v-if="isExpanded"
						class="sources-expanded"
					>
						<!-- Close button -->
						<div class="close-button-container">
							<button
								class="close-button"
								aria-label="Close sources"
								@click="toggleExpanded"
							>
								<svg
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
								>
									<line
										x1="18"
										y1="6"
										x2="6"
										y2="18"
									></line>
									<line
										x1="6"
										y1="6"
										x2="18"
										y2="18"
									></line>
								</svg>
							</button>
						</div>

						<!-- Sources list -->
						<div
							v-for="(source, index) in processedSources"
							:key="index"
							class="source-item"
							@click="openSource(source.url)"
						>
							<img
								:src="source.favicon"
								:alt="source.domain"
								class="source-favicon"
								@error="(e) => handleFaviconError(e, source)"
							/>
							<div class="source-content">
								<div class="source-line">
									<span class="source-domain">{{ source.domain }}</span>
									<span class="source-title">{{ source.title.replaceAll('*', '') }}</span>
								</div>
							</div>
						</div>
					</div>
				</transition>
			</div>

			<!-- Traditional Grounding Supports (when no groundingChunks) -->
			<div
				v-if="
					groundingSupports &&
					groundingSupports.length > 0 &&
					(!processedSources || processedSources.length === 0)
				"
				class="grounding-supports-traditional"
			>
				<h4 class="search-title">{{ $t('SUPER_AI.grounding_sources') }}</h4>
				<div class="grounding-results">
					<div
						v-for="(support, index) in groundingSupports"
						:key="index"
						class="grounding-support-item"
					>
						<a
							v-if="support.segment && support.segment.startIndex !== undefined"
							:href="`#grounding-${index}`"
							class="grounding-support-link"
						>
							<div class="grounding-support-title">Source {{ index + 1 }}</div>
							<div
								v-if="support.segment.text"
								class="grounding-support-text"
							>
								{{ support.segment.text }}
							</div>
						</a>
					</div>
				</div>
			</div>
		</div>
		<!-- End hasSearchSuggestions -->
	</div>
	<!-- End google-search-suggestions -->
</template>

<script setup lang="ts">
	import { computed, ref, watch, nextTick } from 'vue';

	interface Props {
		groundingMetadata?: any;
	}

	interface ProcessedSource {
		url: string;
		domain: string;
		title: string;
		favicon: string;
	}

	const props = defineProps<Props>();

	// Reactive state
	const isExpanded = ref(false);

	// Computed properties to extract search data
	const hasSearchSuggestions = computed(() => {
		if (!props.groundingMetadata) {
			return false;
		}

		const hasSearchEntryPoint = !!props.groundingMetadata.searchEntryPoint;
		const hasSearchQueries = !!(
			props.groundingMetadata.searchQueries &&
			props.groundingMetadata.searchQueries.length > 0
		);
		const hasWebSearchQueries = !!(
			props.groundingMetadata.webSearchQueries &&
			props.groundingMetadata.webSearchQueries.length > 0
		);
		const hasGroundingSupports = !!(
			props.groundingMetadata.groundingSupports &&
			props.groundingMetadata.groundingSupports.length > 0
		);

		return (
			hasSearchEntryPoint ||
			hasSearchQueries ||
			hasWebSearchQueries ||
			hasGroundingSupports
		);
	});

	const searchEntryPoint = computed(() => {
		return props.groundingMetadata?.searchEntryPoint;
	});

	// Ref para o container
	const searchEntryPointContainer = ref<HTMLDivElement | null>(null);

	// Função MAIS SIMPLES POSSÍVEL - criar iframe em JavaScript
	const createIframe = (htmlContent: string) => {
		if (!searchEntryPointContainer.value) return;

		// Limpar container
		searchEntryPointContainer.value.innerHTML = '';
		
		// Criar iframe
		const iframe = document.createElement('iframe');
		iframe.style.width = '100%';
		iframe.style.height = '100%';
		iframe.style.border = 'none';
		
		// Adicionar permissões de sandbox mais permissivas
		iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-popups allow-forms allow-popups-to-escape-sandbox');
		
		// Permitir abrir links em nova aba
		iframe.setAttribute('allow', 'fullscreen');
		
		// Adicionar ao container
		searchEntryPointContainer.value.appendChild(iframe);
		
		// Aguardar iframe carregar e injetar HTML
		iframe.onload = () => {
			const doc = iframe.contentDocument;
			if (doc) {
				doc.body.innerHTML = htmlContent;
				
				// Adicionar event listener para interceptar cliques em links
				doc.body.addEventListener('click', (e) => {
					const target = e.target as HTMLElement;
					const link = target.closest('a');
					if (link) {
						e.preventDefault();
						window.open(link.href, '_blank');
					}
				});
			}
		};
		
		// Forçar load se necessário
		if (iframe.contentDocument) {
			iframe.onload(null as any);
		}
	};

	// Watcher para criar iframe quando o conteúdo mudar
	watch(
		() => searchEntryPoint.value?.renderedContent,
		(newContent) => {
			if (newContent) {
				nextTick(() => {
					createIframe(newContent);
				});
			}
		},
		{ immediate: true }
	);

	const searchQueries = computed(() => {
		return props.groundingMetadata?.searchQueries || [];
	});

	const webSearchQueries = computed(() => {
		return props.groundingMetadata?.webSearchQueries || [];
	});

	const groundingSupports = computed(() => {
		return props.groundingMetadata?.groundingSupports || [];
	});

	// Process sources from groundingChunks (real data from API)
	const processedSources = computed((): ProcessedSource[] => {
		const sources: ProcessedSource[] = [];

		// Process grounding chunks (these contain the real URLs and domains from the API)
		if (props.groundingMetadata?.groundingChunks) {
			props.groundingMetadata.groundingChunks.forEach((chunk: any, index: number) => {
				if (chunk.web?.uri && chunk.web?.title) {
					// Extract domain from title (format: "domain.com")
					const title = chunk.web.title;
					let domain = '';

					// Try to extract domain from the beginning of title
					const domainMatch = title.match(/^([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
					if (domainMatch) {
						domain = domainMatch[1].replace(/^www\./, '');
					} else {
						// Fallback: try to extract from URI
						try {
							const url = new URL(chunk.web.uri);
							domain = url.hostname.replace('www.', '');
						} catch (error) {
							domain = 'unknown.com';
						}
					}

					// Find corresponding grounding support text for this chunk
					let description = '';
					if (props.groundingMetadata?.groundingSupports) {
						const support = props.groundingMetadata.groundingSupports.find(
							(support: any) =>
								support.groundingChunkIndices && support.groundingChunkIndices.includes(index)
						);
						if (support?.segment?.text) {
							description = support.segment.text;
						}
					}

					// If no description found, use a fallback
					if (!description) {
						description = `Informações sobre ${domain}`;
					}

					sources.push({
						url: chunk.web.uri,
						domain,
						title: description,
						favicon: `https://${domain}/favicon.ico`
					});
				}
			});
		}

		return sources;
	});

	// Helper functions for collapsed state
	const getFirstSourceFavicon = (): string => {
		const firstSource = processedSources.value[0];
		return (
			firstSource?.favicon ||
			'https://www.google.com/s2/favicons?domain=google.com&sz=32'
		);
	};

	const getFirstSourceDomain = (): string => {
		const firstSource = processedSources.value[0];
		return firstSource?.domain || 'google.com';
	};

	const getFirstSourceTitle = (): string => {
		const firstSource = processedSources.value[0];
		const totalSources = processedSources.value.length;

		if (totalSources > 1) {
			return `${firstSource?.title || 'Search results'} +${totalSources - 1} more`;
		}
		return firstSource?.title || 'Search results';
	};

	// Favicon error handling with multiple fallbacks
	const handleFaviconError = (event?: Event, source?: ProcessedSource) => {
		const target = event?.target as HTMLImageElement;
		if (target && source) {
			// Try Google's favicon service as fallback
			if (!target.src.includes('google.com/s2/favicons')) {
				target.src = `https://www.google.com/s2/favicons?domain=${source.domain}&sz=32`;
			} else {
				// Final fallback to a generic icon
				target.src = 'https://www.google.com/s2/favicons?domain=google.com&sz=32';
			}
		}
	};
	const emit = defineEmits(['forceScrollToBottom']);

	// Event handlers
	const toggleExpanded = () => {
		isExpanded.value = !isExpanded.value;
		if (isExpanded.value) {
			setTimeout(() => {
				emit('forceScrollToBottom');
			}, 300);
		}
	};

	const openSource = (url: string) => {
		if (url && url !== '#') {
			window.open(url, '_blank', 'noopener,noreferrer');
		}
	};

	// Transition hooks
	const onEnter = (el: Element) => {
		const element = el as HTMLElement;
		element.style.height = '0';
		element.style.opacity = '0';

		// Force reflow
		element.getBoundingClientRect();

		element.style.transition = 'height 0.3s ease, opacity 0.3s ease';
		element.style.height = element.scrollHeight + 'px';
		element.style.opacity = '1';
	};

	const onLeave = (el: Element) => {
		const element = el as HTMLElement;
		element.style.transition = 'height 0.3s ease, opacity 0.3s ease';
		element.style.height = '0';
		element.style.opacity = '0';
	};
</script>

<style scoped>
	/* Main Container */
	.google-search-suggestions {
		margin-top: 1rem;
		animation: fadeInUp 0.3s ease-out;
	}

	/* Search Title */
	.search-title {
		font-size: 0.875rem;
		font-weight: 600;
		color: #1e40af;
		margin-bottom: 0.75rem;
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.search-title::before {
		content: '🔍';
		font-size: 1rem;
	}

	/* Search Entry Point Styles */
	.search-entry-point {
		width: 100%;
		background-color: transparent;
		max-height: 60px !important;
		overflow: hidden;
		padding: 0;
		margin-bottom: 8px;
	}

	/* Override any global styles that might interfere with rendered content */
	.search-entry-point :deep(*) {
		max-width: 100%;
		color: #1f2937 !important;
		background-color: transparent !important;
	}

	.search-entry-point :deep(.chip) {
		background-color: white !important;
		font-weight: 500;
		color: #1f2937 !important;
	}

	.search-entry-point :deep(svg) {
		width: 30px;
		height: 30px;
	}

	.search-entry-point :deep(a) {
		color: #2563eb !important;
		text-decoration: none;
		transition: color 0.2s ease;
	}

	.search-entry-point :deep(a:hover) {
		color: #1d4ed8 !important;
		text-decoration: underline;
	}

	/* Fallback Search Queries Styles */
	.search-queries {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.search-query-item {
		display: flex;
		align-items: center;
	}

	.search-query-link {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		color: #2563eb;
		font-size: 0.875rem;
		text-decoration: none;
		transition: all 0.2s ease;
		padding: 0.5rem;
		border-radius: 0.375rem;
		background-color: #ffffff;
		border: 1px solid #e0f2fe;
	}

	.search-query-link:hover {
		color: #1d4ed8;
		text-decoration: underline;
		background-color: #f8fafc;
		border-color: #7dd3fc;
		transform: translateY(-1px);
	}

	.search-icon {
		color: #3b82f6;
		flex-shrink: 0;
	}

	/* Web Search Results Styles */
	.web-search-results {
		margin-top: 1rem;
		padding-top: 1rem;
		/* border-top: 1px solid #bae6fd; */
	}

	.web-results {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.web-result-item {
		background-color: #ffffff;
		padding: 0.875rem;
		border-radius: 0.5rem;
		border: 1px solid #e0f2fe;
		transition: all 0.2s ease;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
		animation: fadeInUp 0.3s ease-out;
	}

	.web-result-item:hover {
		border-color: #7dd3fc;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transform: translateY(-1px);
	}

	.web-result-item:nth-child(2) {
		animation-delay: 0.1s;
	}
	.web-result-item:nth-child(3) {
		animation-delay: 0.2s;
	}
	.web-result-item:nth-child(4) {
		animation-delay: 0.3s;
	}

	.web-result-link {
		display: block;
		text-decoration: none;
	}

	.web-result-no-link {
		display: block;
	}

	.web-result-title {
		font-size: 0.875rem;
		font-weight: 500;
		color: #1e40af;
		margin-bottom: 0.25rem;
		line-height: 1.4;
	}

	.web-result-link:hover .web-result-title {
		color: #1e3a8a;
	}

	.web-result-snippet {
		font-size: 0.75rem;
		color: #64748b;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Grounding Supports Container */
	.grounding-supports {
		/* margin-top: 1rem;
		padding-top: 1rem; */
		padding-bottom: 4px;
	}

	/* Traditional Grounding Supports Styles */
	.grounding-supports-traditional {
		margin-top: 1rem;
		padding-top: 1rem;
	}

	.grounding-results {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.grounding-support-item {
		background-color: #ffffff;
		padding: 0.875rem;
		border-radius: 0.5rem;
		border: 1px solid #e0f2fe;
		transition: all 0.2s ease;
		animation: fadeInUp 0.3s ease-out;
	}

	.grounding-support-item:hover {
		border-color: #7dd3fc;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.grounding-support-item:nth-child(2) {
		animation-delay: 0.1s;
	}
	.grounding-support-item:nth-child(3) {
		animation-delay: 0.2s;
	}
	.grounding-support-item:nth-child(4) {
		animation-delay: 0.3s;
	}

	.grounding-support-link {
		display: block;
		text-decoration: none;
	}

	.grounding-support-title {
		font-size: 0.875rem;
		font-weight: 500;
		color: #1e40af;
		margin-bottom: 0.25rem;
	}

	.grounding-support-link:hover .grounding-support-title {
		color: #1e3a8a;
	}

	.grounding-support-text {
		font-size: 0.75rem;
		color: #64748b;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Collapsed Summary State */
	.sources-summary {
		background-color: #f8fafc;
		border: 1px solid #e2e8f0;
		border-radius: 10px;
		padding: 0.75rem 1rem;
		margin-left: 6.5px;
		margin-right: 6.5px;
		cursor: pointer;
		transition: all 0.2s ease;
		user-select: none;
	}

	.sources-summary:hover {
		background-color: #f1f5f9;
		border-color: #cbd5e1;
		transform: translateY(-1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	}

	.summary-content {
		display: flex;
		align-items: center;
		gap: 0.75rem;
	}

	.summary-favicon {
		width: 20px;
		height: 20px;
		border-radius: 50%;
		flex-shrink: 0;
		background-color: #e2e8f0;
	}

	.summary-line {
		display: flex;
		align-items: baseline;
		gap: 0.5rem;
		line-height: 1.4;
		flex: 1;
		min-width: 0;
	}

	.summary-domain {
		font-size: 0.875rem;
		font-weight: 600;
		color: #1e40af;
		flex-shrink: 0;
	}

	.summary-title {
		font-size: 0.875rem;
		color: #64748b;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		flex: 1;
	}

	/* Expanded Sources State */
	.sources-expanded {
		background-color: #f8fafc;
		border: 1px solid #e2e8f0;
		border-radius: 0.75rem;
		padding: 0.5rem;
		overflow: hidden;
		position: relative;
	}

	/* Close Button */
	.close-button-container {
		display: flex;
		justify-content: flex-end;
		margin-bottom: 0.5rem;
	}

	.close-button {
		background: none;
		border: none;
		cursor: pointer;
		padding: 0.25rem;
		border-radius: 0.375rem;
		color: #64748b;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.close-button:hover {
		background-color: #e2e8f0;
		color: #475569;
		transform: scale(1.1);
	}

	.close-button:active {
		transform: scale(0.95);
	}

	.source-item {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		padding: 0.75rem;
		border-radius: 0.5rem;
		cursor: pointer;
		transition: all 0.2s ease;
		animation: fadeInUp 0.3s ease-out;
	}

	.source-item:hover {
		background-color: #ffffff;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		transform: translateY(-1px);
	}

	.source-item:nth-child(2) {
		animation-delay: 0.1s;
	}
	.source-item:nth-child(3) {
		animation-delay: 0.2s;
	}
	.source-item:nth-child(4) {
		animation-delay: 0.3s;
	}
	.source-item:nth-child(5) {
		animation-delay: 0.4s;
	}

	.source-favicon {
		width: 20px;
		height: 20px;
		border-radius: 50%;
		flex-shrink: 0;
		background-color: #e2e8f0;
	}

	.source-content {
		display: flex;
		flex-direction: column;
		gap: 0.125rem;
		flex: 1;
		min-width: 0;
	}

	.source-line {
		display: flex;
		align-items: baseline;
		gap: 0.5rem;
		line-height: 1.4;
	}

	.source-domain {
		font-size: 0.875rem;
		font-weight: 600;
		color: #1e40af;
		flex-shrink: 0;
	}

	.source-title {
		font-size: 0.875rem;
		color: #64748b;
		line-height: 1.4;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		flex: 1;
	}

	/* Expand Transition */
	.expand-enter-active,
	.expand-leave-active {
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		overflow: hidden;
	}

	.expand-enter-from,
	.expand-leave-to {
		height: 0 !important;
		opacity: 0;
		padding-top: 0;
		padding-bottom: 0;
		margin-top: 0;
		margin-bottom: 0;
	}

	.expand-enter-to,
	.expand-leave-from {
		opacity: 1;
	}

	/* Responsive Design */
	@media (max-width: 640px) {
		.google-search-suggestions {
			margin-top: 0.75rem;
		}

		.sources-summary,
		.sources-expanded {
			padding: 0.625rem;
		}

		.summary-content,
		.source-item {
			gap: 0.5rem;
		}

		.summary-favicon,
		.source-favicon {
			width: 18px;
			height: 18px;
		}

		.summary-domain,
		.source-domain {
			font-size: 0.8125rem;
		}

		.summary-title,
		.source-title {
			font-size: 0.75rem;
		}
	}

	/* Animation for smooth appearance */
	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
