<template>
	<a-select
		:value="modelValue"
		class="h-[40px]"
		:get-popup-container="
			onPopover
				? (triggerNode: any) => triggerNode.closest('.base-popover') 
				: (triggerNode: any) => triggerNode.parentNode
		"
		placement="bottomLeft"
		@change="handleChange"
	>
		<template #suffixIcon>
			<svg-icon
				type="mdi"
				:path="mdiChevronDown"
				size="16"
				color="var(--grey-500)"
				class="content-icon simple-arrow"
			></svg-icon>
		</template>
		<a-select-option
			v-for="option in options"
			:key="option.value"
			:value="option.value"
			:disabled="option.disabled"
		>
			<div class="flex items-center gap-2">
				<HugeiconsIcon
					:icon="option.icon"
					color="var(--grey-500)"
					:size="18"
				/><span>{{ $t(option.label) }}</span>
				<div
					v-if="option.disabled"
					class="small-chip"
				>
					{{ $t('GLOBAL.soon') }}
				</div>
			</div>
		</a-select-option>
	</a-select>
</template>

<script lang="ts" setup>
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { mdiChevronDown } from '@mdi/js';
	import {
		SquareLock01Icon,
		WebSecurityIcon,
		InternetIcon
	} from '@hugeicons-pro/core-stroke-rounded';

	defineProps({
		modelValue: {
			type: String,
			required: true
		},
		onPopover: {
			type: Boolean,
			default: false
		}
	});

	const options = [
		{
			value: 'PRIVATE',
			label: 'PROJECT.private',
			icon: SquareLock01Icon
		},
		{
			value: 'PUBLIC_WITH_LOGIN',
			label: 'PROJECT.public_with_login',
			icon: WebSecurityIcon
		},
		{
			value: 'PUBLIC_WITHOUT_LOGIN',
			label: 'PROJECT.public',
			icon: InternetIcon,
			disabled: true
		}
	];

	const emit = defineEmits(['update:modelValue', 'change']);

	const handleChange = (value: string) => {
		emit('update:modelValue', value);
		emit('change', value);
	};
</script>
