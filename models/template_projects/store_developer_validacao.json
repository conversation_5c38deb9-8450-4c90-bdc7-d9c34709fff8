{"templateList": [{"empty_template": true, "is_developer_item": true, "title_pt": "<PERSON> branco", "title_en": "No Template", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/Icon-0.svg", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Crie sua aplicação do zero, sem modelo predefinido", "longDescription_en": "Create your application from scratch, without a predefined template", "videoUrl": "", "baseUrl_pt": "", "baseUrl_en": "", "bucketSource": "mitra-multitenant-prod", "identifier": {"key": "3", "3": "OUTRO"}, "types": []}, {"is_developer_item": true, "title_pt": "Mitra + IA", "title_en": "Mitra + AI", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/mitra_ai_template_icon.png", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Projeto de criação de eventos baseado em IA", "longDescription_en": "Project for creating events based on AI", "videoUrl": "", "baseUrl_pt": "backup/store/mitra_ia/mitra_ia.zip", "baseUrl_en": "backup/store/mitra_ia/mitra_ia.zip", "bucketSource": "mitra-multitenant", "new_content": true, "identifier": {"key": "19", "18": "OUTRO"}, "types": []}, {"is_developer_item": true, "title_pt": "Mini ERP", "title_en": "Mini ERP", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/mini_erp_template_icon.svg", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Exemplo de um Mini ERP com cadastros, movimentos e BI", "longDescription_en": "Example of a Mini ERP with records, movements and BI", "videoUrl": "", "baseUrl_pt": "backup/store/miniERP/miniERP.zip", "baseUrl_en": "backup/store/miniERP/miniERP.zip", "bucketSource": "mitra-multitenant", "identifier": {"key": "18", "18": "OUTRO"}, "types": []}, {"is_developer_item": true, "title_pt": "Cadastro Básico", "title_en": "Basic Registration", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/Icon-1.svg", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Exemplo de tabela com CRUD (leitura, edição, deleção e adição)", "longDescription_en": "Example of a table with CRUD (create, read, update, and delete)", "videoUrl": "", "baseUrl_pt": "backup/store/cadastroSimples/cadastroSimples.zip", "baseUrl_en": "backup/store/cadastroSimples/cadastroSimples.zip", "bucketSource": "mitra-multitenant-prod", "identifier": {"key": "14", "14": "CADASTRO_BASICO"}, "types": []}, {"is_developer_item": true, "title_pt": "Cadastro com Detalhes", "title_en": "Detailed Registration", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/Icon-2.svg", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Exemplo de tabela CRUD com tela personalizada de edição", "longDescription_en": "Example of a table with CRUD (create, read, update, and delete)", "videoUrl": "", "baseUrl_pt": "backup/store/cadastroComDetalhes/cadastroComDetalhes.zip", "baseUrl_en": "backup/store/cadastroComDetalhes/cadastroComDetalhes.zip", "bucketSource": "mitra-multitenant-prod", "identifier": {"key": "15", "15": "CADASTRO_COM_DETALHES"}, "types": []}, {"is_developer_item": true, "title_pt": "Fluxo de Processos", "title_en": "Process Flow", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/Icon-3.svg", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Exemplo de tabela CRUD com fases do processo e timeline estilo <PERSON>", "longDescription_en": "Example of a table with CRUD, process phases, and a Kanban-style timeline", "videoUrl": "", "baseUrl_pt": "backup/store/fluxoDeProcessos/fluxoDeProcessos.zip", "baseUrl_en": "backup/store/fluxoDeProcessos/fluxoDeProcessos.zip", "bucketSource": "mitra-multitenant-prod", "identifier": {"key": "1", "1": "FLUXO_DE_PROCESSOS"}, "types": []}, {"is_developer_item": true, "title_pt": "Pesquisa Básica", "title_en": "Basic Search", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/Icon-4.svg", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Exemplo de aplicação de disparo de e-mails com pesquisa personalizada", "longDescription_en": "Example of an email dispatch application with custom search", "videoUrl": "", "baseUrl_pt": "backup/store/pesquisaSatisfação/pesquisaSatisfação.zip", "baseUrl_en": "backup/store/pesquisaSatisfação/pesquisaSatisfação.zip", "bucketSource": "mitra-multitenant-prod", "identifier": {"key": "16", "16": "PESQUISA_BASICA"}, "types": []}, {"is_developer_item": true, "title_pt": "App Mobile Fitness", "title_en": "App Mobile Fitness", "project_icon": "", "project_color": "", "image": "https://raw.githubusercontent.com/mitraecp/frontend-resoucers/master/s3_store/templates_projects/images/Icon-5.svg", "shortDescription_pt": "", "shortDescription_en": "", "longDescription_pt": "Exemplo de app fitness com área de administrador para controle de conteúdo", "longDescription_en": "Example of a fitness app with an admin area for content control", "videoUrl": "", "baseUrl_pt": "backup/store/mobileFitness/mobileFitness.zip", "baseUrl_en": "backup/store/mobileFitness/mobileFitness.zip", "bucketSource": "mitra-multitenant-prod", "identifier": {"key": "17", "17": "MOBILE_FITNESS"}, "types": []}]}