export default function useScrollManager({ elementId }: { elementId: string }) {
	const scrollToElement = (elementId: string) => {
		const element = document.getElementById(elementId);
		if (element) {
			element.scrollIntoView({ behavior: 'smooth' });
		}
	};

	onMounted(() => {
		const hash = useRoute().hash;
		const _hash = hash.slice(1);
		if (_hash === elementId) {
			scrollToElement(elementId);
		}
	});

	watch(
		() => useRoute().hash,
		(hash) => {
			const _hash = hash.slice(1);
			if (_hash === elementId) {
				scrollToElement(elementId);
			}
		}
	);

	return {
		scrollToElement
	};
}
