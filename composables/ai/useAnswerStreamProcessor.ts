/* eslint-disable no-console */
import { ref, computed } from 'vue';
import { superAIPlanKeys } from '~/models/super_ai_plan_keys';

export interface ValidAnswerObject {
    id: string;
    content: any;
    timestamp: number;
    isComplete: boolean;
}

export function useAnswerStreamProcessor() {
    const accumulatedText = ref('');
    const validObjects = ref<ValidAnswerObject[]>([]);
    const processingError = ref<string | null>(null);

    /**
     * Extrai conteúdo de dentro das tags <BuildingSystem>
     */
    const extractBuildingSystemContent = (text: string): string => {
        // console.log('🔍 Processando chunk:', text);

        // Primeiro, tenta extrair conteúdo de tags completas
        const completeTagRegex = /<BuildingSystem>([\s\S]*?)<\/BuildingSystem>/g;
        const completeMatches = text.match(completeTagRegex);

        let extractedContent = '';

        if (completeMatches) {
            extractedContent = completeMatches
                .map(match => match.replace(/<\/?BuildingSystem>/g, ''))
                .join('');
            // console.log('✅ Conteúdo extraído de tags completas:', extractedContent);
        }

        // Também extrai conteúdo de tags abertas (sem fechamento)
        const openTagRegex = /<BuildingSystem>([\s\S]*?)(?=<\/BuildingSystem>|$)/g;
        const openMatches = [...text.matchAll(openTagRegex)];

        if (openMatches.length > 0) {
            const openContent = openMatches
                .map(match => match[1])
                .join('');

            // Se não temos conteúdo de tags completas, usa o conteúdo das tags abertas
            if (!extractedContent && openContent) {
                extractedContent = openContent;
                // console.log('⚠️ Conteúdo extraído de tag aberta:', extractedContent);
            }
        }

        // Se não encontrou nada, verifica se o chunk inteiro está dentro de uma tag
        if (!extractedContent && text.includes('BuildingSystem')) {
            // Remove apenas as tags que encontrar
            extractedContent = text
                .replace(/<BuildingSystem>/g, '')
                .replace(/<\/BuildingSystem>/g, '');
            // console.log('🔧 Conteúdo extraído removendo tags:', extractedContent);
        }

        // Se ainda não encontrou nada, mas o texto parece ser parte de um JSON
        if (!extractedContent && (text.includes('{') || text.includes('}') || text.includes('"'))) {
            // Pode ser um fragmento de JSON sem tags
            extractedContent = text;
            // console.log('🔍 Possível fragmento JSON sem tags:', extractedContent);
        }

        return extractedContent;
    };

    /**
     * Verifica se uma string é um JSON válido
     */
    const isValidJson = (str: string): boolean => {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    };

    /**
     * Extrai objetos individuais válidos de arrays dentro do JSON principal
     */
    const extractValidObjects = (text: string): any[] => {
        // console.log('🔍 Tentando extrair objetos individuais de:', text);

        const objects: any[] = [];
        let currentText = text.trim();

        if (!currentText) {
            // console.log('❌ Texto vazio, nada para extrair');
            return objects;
        }

        // Preserva quebras de linha mas normaliza espaços
        currentText = currentText.replace(/\s+/g, ' ');
        // console.log('📝 Texto normalizado:', currentText);

        // Estratégia principal: Extrair objetos individuais de arrays
        const extractedObjects = extractIndividualObjects(currentText);
        objects.push(...extractedObjects);

        // console.log(`🎯 Total de objetos individuais extraídos: ${objects.length}`);
        return objects;
    };

    /**
     * Extrai objetos individuais de arrays usando todas as chaves do superAIPlanKeys
     * Suporta arrays parciais (sem fechamento ]) para extração progressiva
     */
    const extractIndividualObjects = (text: string): any[] => {
        const individualObjects: any[] = [];

        // Usa as chaves válidas do sistema importadas
        // Cria padrões para arrays completos E parciais
        const arrayPatterns = superAIPlanKeys.map((key: string) => ({
            name: key,
            // Padrão que captura arrays completos [...]
            completePattern: new RegExp(`"${key}":\\s*\\[([\\s\\S]*?)\\]`, 'g'),
            // Padrão que captura arrays parciais [...
            partialPattern: new RegExp(`"${key}":\\s*\\[([\\s\\S]*?)(?=\\s*(?:"\\w+"|$))`, 'g')
        }));

        // console.log('🔍 Padrões de busca criados para:', superAIPlanKeys);

        for (const { name, completePattern, partialPattern } of arrayPatterns) {
            // Primeiro tenta arrays completos
            const completeMatches = [...text.matchAll(completePattern)];

            for (const match of completeMatches) {
                const arrayContent = match[1];
                // console.log(`🔍 Processando array completo ${name}:`, arrayContent);

                // Extrai objetos individuais do array
                const objectsInArray = extractObjectsFromArrayContent(arrayContent, name);
                individualObjects.push(...objectsInArray);
            }

            // Se não encontrou arrays completos, tenta parciais
            if (completeMatches.length === 0) {
                const partialMatches = [...text.matchAll(partialPattern)];

                for (const match of partialMatches) {
                    const arrayContent = match[1];
                    // console.log(`🔍 Processando array parcial ${name}:`, arrayContent);

                    // Extrai objetos individuais do array parcial
                    const objectsInArray = extractObjectsFromArrayContent(arrayContent, name);
                    individualObjects.push(...objectsInArray);
                }
            }
        }

        // Também tenta extrair objetos soltos que podem estar completos
        const standaloneObjects = extractStandaloneObjects(text);
        individualObjects.push(...standaloneObjects);

        return individualObjects;
    };

    /**
     * Extrai objetos individuais do conteúdo de um array
     * Suporta objetos completos e parciais para extração progressiva
     */
    const extractObjectsFromArrayContent = (arrayContent: string, arrayType: string): any[] => {
        const objects: any[] = [];
        let braceCount = 0;
        let startIndex = -1;
        let inString = false;
        let escapeNext = false;

        // console.log(`🔍 Analisando conteúdo do array ${arrayType}:`, arrayContent);

        for (let i = 0; i < arrayContent.length; i++) {
            const char = arrayContent[i];

            if (escapeNext) {
                escapeNext = false;
                continue;
            }

            if (char === '\\') {
                escapeNext = true;
                continue;
            }

            if (char === '"' && !escapeNext) {
                inString = !inString;
                continue;
            }

            if (inString) continue;

            if (char === '{') {
                if (braceCount === 0) {
                    startIndex = i;
                }
                braceCount++;
            } else if (char === '}') {
                braceCount--;

                if (braceCount === 0 && startIndex !== -1) {
                    const potentialObject = arrayContent.substring(startIndex, i + 1);
                    // console.log(`🧪 Testando objeto completo em ${arrayType}:`, potentialObject);

                    if (isValidJson(potentialObject)) {
                        try {
                            const parsed = JSON.parse(potentialObject);
                            // Adiciona metadados sobre o tipo de array
                            const objectWithMeta = {
                                ...parsed,
                                _arrayType: arrayType,
                                _extractedAt: Date.now()
                            };
                            objects.push(objectWithMeta);
                            // console.log(`✅ Objeto completo extraído de ${arrayType}:`, parsed);
                        } catch (e) {
                            // console.log('❌ Erro ao fazer parse do objeto completo:', e);
                        }
                    } else {
                        // console.log('❌ Objeto JSON inválido');
                    }

                    startIndex = -1;
                }
            }
        }

        // Se ainda há um objeto sendo construído (braceCount > 0), tenta extrair parcialmente
        if (startIndex !== -1 && braceCount > 0) {
            const partialObject = arrayContent.substring(startIndex);
            // console.log(`🔄 Tentando extrair objeto parcial de ${arrayType}:`, partialObject);

            // Tenta completar o objeto parcial adicionando chaves fechadas
            const attempts = [
                partialObject + '}',
                partialObject + '"}',
                partialObject + '"}'
            ];

            for (const attempt of attempts) {
                if (isValidJson(attempt)) {
                    try {
                        const parsed = JSON.parse(attempt);
                        const objectWithMeta = {
                            ...parsed,
                            _arrayType: arrayType,
                            _extractedAt: Date.now(),
                            _isPartial: true // Marca como parcial
                        };
                        objects.push(objectWithMeta);
                        // console.log(`✅ Objeto parcial extraído de ${arrayType}:`, parsed);
                        break;
                    } catch (e) {
                        // Continua tentando
                    }
                }
            }
        }

        // console.log(`📊 Total de objetos extraídos de ${arrayType}: ${objects.length}`);
        return objects;
    };

    /**
     * Extrai objetos que podem estar soltos no texto (não em arrays)
     */
    const extractStandaloneObjects = (text: string): any[] => {
        const objects: any[] = [];
        let braceCount = 0;
        let startIndex = -1;
        let inString = false;
        let escapeNext = false;

        for (let i = 0; i < text.length; i++) {
            const char = text[i];

            if (escapeNext) {
                escapeNext = false;
                continue;
            }

            if (char === '\\') {
                escapeNext = true;
                continue;
            }

            if (char === '"' && !escapeNext) {
                inString = !inString;
                continue;
            }

            if (inString) continue;

            if (char === '{') {
                if (braceCount === 0) {
                    startIndex = i;
                }
                braceCount++;
            } else if (char === '}') {
                braceCount--;

                if (braceCount === 0 && startIndex !== -1) {
                    const potentialObject = text.substring(startIndex, i + 1);

                    // Verifica se é um objeto standalone (não parte de um array conhecido)
                    const beforeObject = text.substring(Math.max(0, startIndex - 50), startIndex);
                    const isPartOfArray = /("ADD_\w+":\s*\[\s*)$/.test(beforeObject);

                    if (!isPartOfArray && isValidJson(potentialObject)) {
                        try {
                            const parsed = JSON.parse(potentialObject);
                            // Verifica se tem propriedades que indicam ser um objeto útil
                            if (hasUsefulProperties(parsed)) {
                                const objectWithMeta = {
                                    ...parsed,
                                    _arrayType: 'STANDALONE',
                                    _extractedAt: Date.now()
                                };
                                objects.push(objectWithMeta);
                                // console.log('✅ Objeto standalone válido encontrado:', parsed);
                            }
                        } catch (e) {
                            // Ignora erros de parsing
                        }
                    }

                    startIndex = -1;
                }
            }
        }

        return objects;
    };

    /**
     * Verifica se um objeto tem propriedades úteis para renderização
     */
    const hasUsefulProperties = (obj: any): boolean => {
        const usefulProps = ['name', 'screenName', 'text', 'title', 'x', 'y', 'w', 'h', 'fontSize', 'backgroundColor'];
        return usefulProps.some(prop => Object.prototype.hasOwnProperty.call(obj, prop));
    };

    /**
     * Processa um novo chunk de texto
     */
    const processChunk = (chunk: string): ValidAnswerObject[] => {
        try {
            // console.log('🚀 Processando novo chunk...');
            processingError.value = null;

            // Extrai conteúdo das tags BuildingSystem
            const buildingSystemContent = extractBuildingSystemContent(chunk);

            if (!buildingSystemContent) {
                // console.log('⚠️ Nenhum conteúdo BuildingSystem encontrado no chunk');
                return [];
            }

            // console.log('📥 Conteúdo extraído:', buildingSystemContent);

            // Acumula o texto
            accumulatedText.value += buildingSystemContent;

            // console.log('📚 Texto acumulado total:', accumulatedText.value);
            // console.log('📊 Tamanho do texto acumulado:', accumulatedText.value.length);

            // Tenta extrair objetos válidos
            const extractedObjects = extractValidObjects(accumulatedText.value);

            // console.log('🎯 Objetos extraídos nesta iteração:', extractedObjects.length);

            // Cria objetos válidos com metadados
            const newValidObjects: ValidAnswerObject[] = [];

            extractedObjects.forEach((obj, index) => {
                const objectId = `answer-obj-${Date.now()}-${index}`;
                const validObject: ValidAnswerObject = {
                    id: objectId,
                    content: obj,
                    timestamp: Date.now(),
                    isComplete: true
                };

                // Verifica se já não existe um objeto igual
                const exists = validObjects.value.some(existing =>
                    JSON.stringify(existing.content) === JSON.stringify(obj)
                );

                if (!exists) {
                    newValidObjects.push(validObject);
                    validObjects.value.push(validObject);
                    // console.log('✅ Novo objeto válido adicionado:', obj);
                } else {
                    // console.log('🔄 Objeto já existe, ignorando duplicata');
                }
            });

            // console.log(`📈 Total de objetos válidos agora: ${validObjects.value.length}`);
            // console.log(`🆕 Novos objetos neste chunk: ${newValidObjects.length}`);

            return newValidObjects;

        } catch (error) {
            processingError.value = `Erro ao processar chunk: ${error}`;
            console.error('❌ Erro no processamento do chunk:', error);
            return [];
        }
    };

    /**
     * Limpa todos os dados acumulados
     */
    const clearAccumulated = () => {
        accumulatedText.value = '';
        validObjects.value = [];
        processingError.value = null;
    };

    /**
     * Obtém apenas os objetos válidos mais recentes
     */
    const getLatestValidObjects = computed(() => {
        return validObjects.value.slice().reverse();
    });

    /**
     * Obtém o texto acumulado atual (para debug)
     */
    const getCurrentAccumulatedText = computed(() => {
        return accumulatedText.value;
    });

    /**
     * Obtém estatísticas do processamento
     */
    const getProcessingStats = computed(() => {
        return {
            totalObjects: validObjects.value.length,
            accumulatedTextLength: accumulatedText.value.length,
            hasError: !!processingError.value,
            errorMessage: processingError.value
        };
    });

    return {
        processChunk,
        clearAccumulated,
        validObjects: getLatestValidObjects,
        accumulatedText: getCurrentAccumulatedText,
        processingStats: getProcessingStats,
        processingError: computed(() => processingError.value)
    };
}
