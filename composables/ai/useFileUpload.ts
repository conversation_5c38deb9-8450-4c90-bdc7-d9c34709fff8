import { ref } from 'vue';
import {
	getStorage,
	ref as storageRef,
	uploadBytes,
	getDownloadURL
} from 'firebase/storage';

export interface UploadedFileMetadata {
	id: string;
	name: string;
	size: number;
	type: string;
	url: string;
	uploadedAt: string;
	messageId: string;
}

export const useFileUpload = () => {
	const uploading = ref(false);
	const uploadProgress = ref(0);

	/**
	 * Upload a file to Firebase Storage and return metadata
	 * @param file - File to upload
	 * @param messageId - ID of the message this file belongs to
	 * @returns Promise with file metadata
	 */
	const uploadFileToFirebase = async (
		file: File,
		messageId: string
	): Promise<UploadedFileMetadata> => {
		try {
			uploading.value = true;
			uploadProgress.value = 0;

			const storage = getStorage();
			const fileId =  useRandomID().generateSimpleUniqueId();
			const fileName = `${fileId}_${file.name}`;

			// Create storage path: ai-chat-files/{workspaceId}/{projectId}/{messageId}/{fileName}
			const workspaceId = useWorkspaceStore().selectedWorkspace.id;
			const projectId = useWorkspaceStore().selectedProject.id;
			const userId = useUserStore().loggedUser?.id;

			if (!userId) {
				throw new Error('User not authenticated');
			}

			const filePath = `ai-chat-files/${workspaceId}/${projectId}/${userId}/${messageId}/${fileName}`;
			const fileRef = storageRef(storage, filePath);

			// Upload file
			const snapshot = await uploadBytes(fileRef, file);

			// Get download URL
			const downloadURL = await getDownloadURL(snapshot.ref);

			uploadProgress.value = 100;

			const metadata: UploadedFileMetadata = {
				id: fileId,
				name: file.name,
				size: file.size,
				type: file.type,
				url: downloadURL,
				uploadedAt: new Date().toISOString(),
				messageId
			};

			return metadata;
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error('Error uploading file:', error);
			throw error;
		} finally {
			uploading.value = false;
			uploadProgress.value = 0;
		}
	};

	/**
	 * Upload multiple files to Firebase Storage
	 * @param files - Array of files to upload
	 * @param messageId - ID of the message these files belong to
	 * @returns Promise with array of file metadata
	 */
	const uploadMultipleFiles = (
		files: File[],
		messageId: string
	): Promise<UploadedFileMetadata[]> =>
		Promise.all(files.map((file) => uploadFileToFirebase(file, messageId)));

	/**
	 * Check if a file type is an image
	 * @param fileType - MIME type of the file
	 * @returns boolean indicating if file is an image
	 */
	const isImageFile = (fileType: string): boolean => {
		return fileType.startsWith('image/');
	};

	/**
	 * Check if a file type is a document
	 * @param fileType - MIME type of the file
	 * @returns boolean indicating if file is a document
	 */
	const isDocumentFile = (fileType: string): boolean => {
		const documentTypes = [
			'application/pdf',
			'text/plain',
			'text/html',
			'text/css',
			'text/markdown',
			'text/csv',
			'text/xml',
			'text/rtf',
			'application/x-javascript',
			'text/javascript',
			'application/x-python',
			'text/x-python'
		];
		return documentTypes.includes(fileType) || fileType.startsWith('text/');
	};

	return {
		uploading,
		uploadProgress,
		uploadFileToFirebase,
		uploadMultipleFiles,
		isImageFile,
		isDocumentFile
	};
};
