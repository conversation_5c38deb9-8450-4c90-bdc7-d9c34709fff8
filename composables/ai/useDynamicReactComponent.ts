export function useDynamicReactComponent(TOKEN_LEGADO_MITRA: any) {
    function getBarWidgetShadcn(message: any) {
        const query = message?.query ?? message?.[0]?.query ?? '';
        const chartConfigInput = message?.chartConfig ?? message?.[0]?.chartConfig ?? {};

        return `
            import React, { useState, useEffect, useMemo } from 'react';
            import { Bar, BarChart, CartesianGrid, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from 'recharts';
            import {
            Card,
            CardContent,
            CardDescription,
            CardFooter,
            CardHeader,
            CardTitle,
            } from "@/components/ui/card";
            import {
            ChartContainer,
            ChartTooltip,
            ChartTooltipContent,
            ChartLegend,
            ChartLegendContent,
            } from "@/components/ui/chart"; 
            import * as LucideReact from 'lucide-react';
            
            type ChartDataPoint = Record<string, string | number | Date>;
            
            const defaultChartConfig = {
            };
            
            export function BarChartDemo() {
            const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
            const [isLoading, setIsLoading] = useState(true);
            const [error, setError] = useState<Error | null>(null);
            
            const [xAxisKey, setXAxisKey] = useState<string | null>(${JSON.stringify(chartConfigInput.xAxisKey ?? null)});
            const [yAxisKeys, setYAxisKeys] = useState<Array<{ key: string; color: string; label?: string }>>(${JSON.stringify(chartConfigInput.yAxisKeys ?? [])});
            const [yAxisLabel, setYAxisLabel] = useState<string | null>(${JSON.stringify(chartConfigInput.yAxisLabel ?? null)});
            const [chartTitle, setChartTitle] = useState<string | null>(${JSON.stringify(chartConfigInput.chartTitle ?? "Gráfico de Barras")});
            const [chartDescription, setChartDescription] = useState<string | null>(${JSON.stringify(chartConfigInput.chartDescription ?? null)});
            
            const defaultBarColors = [
                "hsl(var(--chart-1))",
                "hsl(var(--chart-2))",
                "hsl(var(--chart-3))",
                "hsl(var(--chart-4))",
                "hsl(var(--chart-5))",
            ];
            
            useEffect(() => {
                const fetchData = async () => {
                setIsLoading(true);
                setError(null);
                
                try {
                    const queryString = \`${query}\`;
                    if (!queryString) {
                    console.warn("Query está vazia. Usando dados de simulação para o gráfico de barras.");
                    const simData = {
                        contents: [
                        { month: "Jan", desktop: 186, mobile: 80 },
                        { month: "Fev", desktop: 305, mobile: 200 },
                        { month: "Mar", desktop: 237, mobile: 120 },
                        { month: "Abr", desktop: 73, mobile: 190 },
                        { month: "Mai", desktop: 209, mobile: 130 },
                        { month: "Jun", desktop: 214, mobile: 140 },
                        ],
                        config: {
                        xAxisKey: "month",
                        yAxisKeys: [
                            { key: "desktop", color: "hsl(var(--chart-1))", label: "Desktop" },
                            { key: "mobile", color: "hsl(var(--chart-2))", label: "Mobile" },
                        ],
                        yAxisLabel: " بازدیدها", 
                        chartTitle: "Visitas Mensais por Dispositivo (Simulado)",
                        chartDescription: "Janeiro - Junho 2024"
                        }
                    };
                    if (simData.contents && Array.isArray(simData.contents)) {
                        setChartData(simData.contents as ChartDataPoint[]);
                        const config = simData.config;
                        setXAxisKey(config.xAxisKey || (simData.contents[0] ? Object.keys(simData.contents[0])[0] : null));
                        
                        let yKeys = config.yAxisKeys || [];
                        if (yKeys.length === 0 && simData.contents[0]) {
                            const firstDataPointKeys = Object.keys(simData.contents[0]);
                            yKeys = firstDataPointKeys
                                .filter(key => key !== config.xAxisKey && typeof simData.contents[0][key] === 'number')
                                .map((key, index) => ({ 
                                    key, 
                                    color: defaultBarColors[index % defaultBarColors.length],
                                    label: key.charAt(0).toUpperCase() + key.slice(1) 
                                }));
                        }
                        setYAxisKeys(yKeys);
                        setYAxisLabel(config.yAxisLabel || null);
                        setChartTitle(config.chartTitle || "Gráfico de Barras (Simulado)");
                        setChartDescription(config.chartDescription || null);
            
                    } else {
                        throw new Error("Dados de simulação inválidos.");
                    }
                    setIsLoading(false);
                    return;
                    }
            
                    const result = await queryMitraWidget({ AuthorizationToken: "${TOKEN_LEGADO_MITRA}" }, queryString);
            
                    if (result && result.contents && Array.isArray(result.contents) && result.contents.length > 0) {
                    setChartData(result.contents as ChartDataPoint[]);
                    
                    const apiConfig = result.config || {};
                    
                    const inferredXAxisKey = apiConfig.xAxisKey || (result.contents[0] ? Object.keys(result.contents[0])[0] : null);
                    setXAxisKey(inferredXAxisKey);
            
                    let finalYAxisKeys = apiConfig.yAxisKeys || [];
                    if (!Array.isArray(finalYAxisKeys) || finalYAxisKeys.length === 0) {
                        const firstDataPointKeys = Object.keys(result.contents[0]);
                        finalYAxisKeys = firstDataPointKeys
                        .filter(key => key !== inferredXAxisKey && typeof result.contents[0][key] === 'number')
                        .map((key, index) => ({
                            key,
                            color: defaultBarColors[index % defaultBarColors.length],
                            label: key.charAt(0).toUpperCase() + key.slice(1)
                        }));
                    } else {
                        finalYAxisKeys = finalYAxisKeys.map((item, index) => ({
                            ...item,
                            key: String(item.key),
                            color: item.color || defaultBarColors[index % defaultBarColors.length],
                            label: item.label || String(item.key).charAt(0).toUpperCase() + String(item.key).slice(1)
                        }));
                    }
                    setYAxisKeys(finalYAxisKeys);
                    
                    setYAxisLabel(apiConfig.yAxisLabel || null);
                    setChartTitle(apiConfig.chartTitle || "Gráfico de Barras");
                    setChartDescription(apiConfig.chartDescription || null);
            
                    } else if (result && Array.isArray(result.contents) && result.contents.length === 0) {
                    setChartData([]); 
                    setChartTitle(chartConfigInput.chartTitle || "Gráfico de Barras");
                    setChartDescription(chartConfigInput.chartDescription || "Nenhum dado para exibir.");
                    setYAxisKeys([]); 
                    } else {
                    console.warn("queryMitraWidget retornou dados em formato inesperado ou vazio.");
                    throw new Error("Dados inválidos ou vazios recebidos da query.");
                    }
                } catch (err: any) {
                    console.error("Erro ao buscar ou processar dados para o gráfico:", err);
                    setError(err);
                } finally {
                    setIsLoading(false);
                }
                };
            
                fetchData();
            }, []); 
            
            const chartConfigForShadcn = useMemo(() => {
                const config: Record<string, { label: string; color: string }> = {};
                yAxisKeys.forEach(item => {
                config[item.key] = {
                    label: item.label || item.key,
                    color: item.color,
                };
                });
                return config;
            }, [yAxisKeys]);
            
            
            if (isLoading) {
                return (
                <div className='flex-grow flex flex-col items-center justify-center h-[300px] text-muted-foreground rounded'>
                        <LucideReact.Loader2 className="h-8 w-8 animate-spin" color="#333" />
                    </div>
                );
            }
            
            if (error) {
                return (
                <Card className="w-full h-[400px]">
                    <CardHeader>
                    <CardTitle className="text-destructive">Erro ao Carregar Gráfico</CardTitle>
                    <CardDescription className="text-destructive">
                        {error.message || "Ocorreu um erro desconhecido."}
                    </CardDescription>
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center h-full">
                        <LucideReact.AlertTriangle className="h-12 w-12 text-destructive mb-4"/>
                        <p>Não foi possível carregar os dados do gráfico.</p>
                    </CardContent>
                </Card>
                );
            }
            
            if (chartData.length === 0 && !isLoading) {
                return (
                <Card className="w-full h-[250px]">
                    <CardContent className="flex flex-col items-center justify-center h-[250px]">
                        <LucideReact.BarChart3 className="h-12 w-12 text-muted-foreground mb-4"/>
                        <p className="text-muted-foreground">Nenhum dado disponível para exibir o gráfico.</p>
                    </CardContent>
                </Card>
                );
            }
            
            
            return (
                <ChartContainer config={chartConfigForShadcn} className="h-full w-full p-4">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid vertical={false} strokeDasharray="3 3" />
                        {xAxisKey && <XAxis dataKey={xAxisKey} tickLine={false} axisLine={false} tickMargin={8} />}
                        <YAxis label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', dy: 70, style: { textAnchor: 'middle', fontSize: '0.8rem', fill: 'hsl(var(--muted-foreground))' } } : undefined} 
                                tickLine={false} 
                                axisLine={false} 
                                tickMargin={8}
                                tickFormatter={(value) => typeof value === 'number' ? value.toLocaleString() : value}
                        />
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent indicator="dashed" />}
                        />
                        {yAxisKeys.length > 1 && (
                            <ChartLegend 
                            content={<ChartLegendContent />}
                            layout="horizontal"
                            verticalAlign="bottom"
                            align="center"
                            wrapperStyle={{ paddingTop: '10px' }}
                            />
                        )}
                        {yAxisKeys.map((item) => (
                            <Bar
                            key={item.key}
                            dataKey={item.key}
                            fill={item.color}
                            radius={4}
                            />
                        ))}
                        </BarChart>
                    </ResponsiveContainer>
                    </ChartContainer>
            );
            }
        `;
    }
    function getPieWidgetShadcn(message: any) {
        const query = message?.query ?? message?.[0]?.query ?? '';
        const chartConfigInput = message?.chartConfig ?? message?.[0]?.chartConfig ?? {};

        return `
            import React, { useState, useEffect, useMemo } from 'react';
            import { PieChart, Pie, Cell, Tooltip, Legend, ResponsiveContainer } from 'recharts';
            import {
            Card,
            CardContent,
            CardDescription,
            CardHeader,
            } from "@/components/ui/card";
            import {
            ChartContainer,
            ChartTooltip,
            ChartTooltipContent,
            ChartLegend,
            ChartLegendContent,
            } from "@/components/ui/chart";
            import { Button } from "@/components/ui/button";
            import * as LucideReact from 'lucide-react';
            
            type PieDataPoint = Record<string, string | number | Date> & { fill?: string; name?: string; value?: number };
            
            const defaultPieColors = [
            "#4361ee", "#f72585", "#4cc9f0", "#7209b7", "#3a0ca3",
            "#f94144", "#f8961e", "#90be6d", "#43aa8b", "#277da1"
            ];
            
            export function PieChartDemo() {
            const [pieData, setPieData] = useState<PieDataPoint[]>([]);
            const [isLoading, setIsLoading] = useState(true);
            const [error, setError] = useState<Error | null>(null);
            
            const [nameKey, setNameKey] = useState<string | null>(${JSON.stringify(chartConfigInput.nameKey ?? null)});
            const [dataKey, setDataKey] = useState<string | null>(${JSON.stringify(chartConfigInput.dataKey ?? null)});
            const [chartDescription, setChartDescription] = useState<string | null>(${JSON.stringify(chartConfigInput.chartDescription ?? null)});
            const [showLegend, setShowLegend] = useState<boolean>(${JSON.stringify(chartConfigInput.showLegend ?? true)});
            
            useEffect(() => {
                const fetchData = async () => {
                setIsLoading(true);
                setError(null);
                setPieData([]);
                
                try {
                    const queryString = \`${query}\`;
                    if (!queryString) {
                    throw new Error("A query está vazia. Não é possível buscar dados para o gráfico.");
                    }
            
                    const result = await queryMitraWidget({ AuthorizationToken: "${TOKEN_LEGADO_MITRA}" }, queryString);
                    const apiConfig = result?.config || {}; 
                    const chartConfigFromMessage = ${JSON.stringify(chartConfigInput)};
            
                    if (result && result.contents && Array.isArray(result.contents)) {
                    if (result.contents.length > 0) {
                        const firstItem = result.contents[0];
                        const inferredNameKey = apiConfig.nameKey || chartConfigFromMessage.nameKey || (firstItem ? Object.keys(firstItem).find(k => typeof firstItem[k] === 'string' || typeof firstItem[k] === 'number') : null);
                        const inferredDataKey = apiConfig.dataKey || chartConfigFromMessage.dataKey || (firstItem ? Object.keys(firstItem).find(k => k !== inferredNameKey && typeof firstItem[k] === 'number') : null);
            
                        if (!inferredNameKey || !inferredDataKey) {
                        throw new Error("Configuração de chaves (nameKey/dataKey) ausente ou não pôde ser inferida dos dados.");
                        }
            
                        setNameKey(inferredNameKey);
                        setDataKey(inferredDataKey);
            
                        const processedData = result.contents.map((item: any, index: number) => {
                        const color = defaultPieColors[index % defaultPieColors.length];
                        return {
                            ...item,
                            name: String(item[inferredNameKey] ?? \`Item \${index + 1}\`),
                            value: parseFloat(String(item[inferredDataKey]).replace(',', '.')) || 0,
                            fill: color, 
                        };
                        });
                        setPieData(processedData);
                    } else {
                        setPieData([]); 
                    }
                    
                    setChartDescription(apiConfig.chartDescription ?? chartConfigFromMessage.chartDescription ?? null);
                    setShowLegend(apiConfig.showLegend !== undefined ? apiConfig.showLegend : (chartConfigFromMessage.showLegend !== undefined ? chartConfigFromMessage.showLegend : true));
            
                    } else {
                    throw new Error("Dados inválidos ou em formato inesperado recebidos da query.");
                    }
                } catch (err: any) {
                    setError(err);
                    setPieData([]);
                } finally {
                    setIsLoading(false);
                }
                };
                fetchData();
            }, []);
            
            const chartConfigForShadcn = useMemo(() => {
                const config: Record<string, { label: string; color: string; icon?: React.ElementType }> = {};
                pieData.forEach((item, index) => {
                if (item.name) {
                    const color = item.fill || defaultPieColors[index % defaultPieColors.length];
                    config[String(item.name)] = {
                    label: String(item.name),
                    color: color,
                    };
                }
                });
                return config;
            }, [pieData]);
            
            if (isLoading) {
                return (
                <div className='flex-grow flex flex-col items-center justify-center h-[300px] text-muted-foreground rounded'>
                        <LucideReact.Loader2 className="h-8 w-8 animate-spin" color="#333" />
                    </div>
                );
            }
            
            if (error) {
                return (
                <Card className="w-full h-[400px]">
                    <CardHeader>
                    <CardTitle className="text-destructive">Erro ao Carregar Gráfico</CardTitle>
                    <CardDescription className="text-destructive">
                        {error.message || "Ocorreu um erro desconhecido."}
                    </CardDescription>
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center h-full">
                        <LucideReact.AlertTriangle className="h-12 w-12 text-destructive mb-4"/>
                        <p>Não foi possível carregar os dados do gráfico.</p>
                    </CardContent>
                </Card>
                );
            }
            
            if (pieData.length === 0 && !isLoading) {
                return (
                <Card className="w-full h-[250px]">
                    <CardContent className="flex flex-col items-center justify-center h-[250px]">
                        <LucideReact.BarChart3 className="h-12 w-12 text-muted-foreground mb-4"/>
                        <p className="text-muted-foreground">Nenhum dado disponível para exibir o gráfico.</p>
                    </CardContent>
                </Card>
                );
            }
            
            
            return (
                
                    <ChartContainer
                    config={chartConfigForShadcn}
                    className="w-full h-full" 
                    >
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent 
                                        hideLabel 
                                        nameKey="name"
                                        formatter={(value, name, item) => {
                                            const percentage = item.payload?.percent;
                                            const formattedValue = typeof value === 'number' ? value.toLocaleString() : String(value);
                                            const label = item.payload?.name || name;
                                            return [
                                                formattedValue,
                                                percentage ? \`\${label} (\${(percentage * 100).toFixed(1)}%)\` : label
                                            ];
                                        }}
                                        className="text-xs min-w-[150px] !p-2 shadow-lg rounded-md bg-popover text-popover-foreground"
                                        indicator="dot"
                                    />}
                        />
                        <Pie
                            data={pieData}
                            dataKey="value"
                            nameKey="name"
                            cx="50%"
                            cy="50%"
                            outerRadius="80%"
                            innerRadius="45%"
                            labelLine={false}
                        >
                            {pieData.map((entry, index) => (
                            <Cell 
                                key={\`cell-\${index}\`} 
                                fill={entry.fill} 
                                strokeWidth={1} 
                                stroke="#ffffff"
                            />
                            ))}
                        </Pie>
                        {showLegend && pieData.length > 0 && (
                            <ChartLegend
                                content={<ChartLegendContent nameKey="name" className="text-xs [&>button]:p-1 [&>button]:h-auto flex-wrap justify-center" />}
                                className="flex items-center justify-center"
                                verticalAlign="bottom"
                                wrapperStyle={{paddingTop: '8px', paddingBottom: '0px', boxSizing: 'content-box'}}
                                height={pieData.length > 4 ? 40 : 25}
                            />
                        )}
                        </PieChart>
                    </ResponsiveContainer>
                    </ChartContainer>
            );
            }
        `;
    }

    function getAreaChartShadcn(message: any) {
        const query = message?.query ?? message?.[0]?.query ?? '';
        const chartConfigInput = message?.chartConfig ?? message?.[0]?.chartConfig ?? {};
        const initialChartHeight = chartConfigInput.chartHeight || '400px';

        return `
            import React, { useState, useEffect, useMemo, useCallback } from 'react';
            import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
            import {
            Card,
            CardContent,
            CardDescription,
            CardHeader,
            CardTitle,
            } from "@/components/ui/card";
            import {
            ChartContainer,
            ChartTooltip,
            ChartTooltipContent,
            ChartLegend,
            ChartLegendContent,
            } from "@/components/ui/chart";
            import * as LucideReact from 'lucide-react';
            
            type AreaDataPoint = Record<string, string | number | Date | null>;
            
            interface AreaSeriesConfig {
            dataKey: string;
            name: string;
            color: string;
            strokeColor?: string; 
            fillOpacity?: number;
            strokeWidth?: number;
            type?: "monotone" | "linear" | "step" | "natural";
            stackId?: string;
            gradientId: string;
            }
            
            const defaultAreaColors = [
            "hsl(var(--chart-1))", "hsl(var(--chart-2))", "hsl(var(--chart-3))",
            "hsl(var(--chart-4))", "hsl(var(--chart-5))", "#10b981", 
            "#3b82f6", "#8b5cf6", "#f59e0b", "#ec4899", 
            ];
            
            export function AreaChartDemo() {
            const [areaData, setAreaData] = useState<AreaDataPoint[]>([]);
            const [isLoading, setIsLoading] = useState(true);
            const [error, setError] = useState<Error | null>(null);
            
            const [xAxisKey, setXAxisKey] = useState<string | null>(${JSON.stringify(chartConfigInput.xAxisKey ?? null)});
            const [seriesConfigs, setSeriesConfigs] = useState<AreaSeriesConfig[]>([]);
            const [chartTitle, setChartTitle] = useState<string>(${JSON.stringify(chartConfigInput.title ?? "Gráfico de Área")});
            const [chartDescription, setChartDescription] = useState<string | null>(${JSON.stringify(chartConfigInput.description ?? null)});
            const [showLegend, setShowLegend] = useState<boolean>(${JSON.stringify(chartConfigInput.showLegend ?? true)});
            const [stackAreas, setStackAreas] = useState<boolean>(${JSON.stringify(chartConfigInput.stackAreas ?? false)});
            const [areaType, setAreaType] = useState<"monotone" | "linear" | "step" | "natural">(${JSON.stringify(chartConfigInput.areaType ?? "monotone")});
            const [yAxisLabel, setYAxisLabel] = useState<string | null>(${JSON.stringify(chartConfigInput.yAxisLabel ?? null)});
            const [xAxisLabel, setXAxisLabel] = useState<string | null>(${JSON.stringify(chartConfigInput.xAxisLabel ?? null)});
            const [valueFormatOptions, setValueFormatOptions] = useState<Intl.NumberFormatOptions | null>(
                ${JSON.stringify(chartConfigInput.valueFormatOptions ?? { minimumFractionDigits: 0, maximumFractionDigits: 1 })}
            );
            const [currentChartHeight, setCurrentChartHeight] = useState<string>("${initialChartHeight}");
            
            
            const formatValue = useCallback((value: number | string | null | undefined) => {
                if (typeof value !== 'number') return String(value ?? '');
                try {
                return value.toLocaleString(undefined, valueFormatOptions || undefined);
                } catch (e) {
                return value.toString();
                }
            }, [valueFormatOptions]);
            
            const fetchData = useCallback(async () => {
                setIsLoading(true);
                setError(null);
            
                const queryString = \`${query}\`;
                const token = "${TOKEN_LEGADO_MITRA}";
                const configFromMessage = ${JSON.stringify(chartConfigInput)};
                
                try {
                if (!queryString) { 
                    throw new Error("A query SQL está vazia. Não é possível buscar dados.");
                }
            
                const result = await queryMitraWidget({ AuthorizationToken: token }, queryString);
                
                const apiConfig = result?.config || {}; 
            
                if (result && result.contents && Array.isArray(result.contents)) {
                    if (result.contents.length === 0) {
                    setAreaData([]);
                    setChartTitle(apiConfig.title || configFromMessage.title || "Gráfico de Área");
                    setChartDescription(apiConfig.description || configFromMessage.description || "Nenhum dado para exibir.");
                    setShowLegend(apiConfig.showLegend !== undefined ? apiConfig.showLegend : (configFromMessage.showLegend !== undefined ? configFromMessage.showLegend : true));
                    setCurrentChartHeight(apiConfig.chartHeight || configFromMessage.chartHeight || "${initialChartHeight}");
                    setIsLoading(false);
                    return;
                    }
            
                    const firstItem = result.contents[0];
            
                    let resolvedXAxisKey = configFromMessage.xAxisKey || apiConfig.xAxisKey;
                    if (!resolvedXAxisKey && firstItem) {
                    resolvedXAxisKey = Object.keys(firstItem).find(k => typeof firstItem[k] === 'string' || firstItem[k] instanceof Date || (typeof firstItem[k] === 'number' && String(firstItem[k]).length === 4)) || Object.keys(firstItem)[0];
                    }
                    setXAxisKey(resolvedXAxisKey);
            
                    let resolvedYAxisKeys: string[] = configFromMessage.yAxisKeys || apiConfig.yAxisKeys;
                    if ((!resolvedYAxisKeys || resolvedYAxisKeys.length === 0) && firstItem && resolvedXAxisKey) {
                    resolvedYAxisKeys = Object.keys(firstItem).filter(
                        k => k !== resolvedXAxisKey && (typeof firstItem[k] === 'number' || (typeof firstItem[k] === 'string' && !isNaN(parseFloat(String(firstItem[k]).replace(',', '.')))))
                    );
                    }
                    if (!resolvedYAxisKeys || resolvedYAxisKeys.length === 0) {
                    throw new Error("Não foi possível inferir as colunas de dados (yAxisKeys) para o gráfico.");
                    }
            
                    const resolvedAreaNames = configFromMessage.areaNames || apiConfig.areaNames || resolvedYAxisKeys;
                    const resolvedColorsInput = configFromMessage.colors || apiConfig.colors || [];
                    const resolvedStackAreas = apiConfig.stackAreas !== undefined ? apiConfig.stackAreas : (configFromMessage.stackAreas !== undefined ? configFromMessage.stackAreas : false);
                    const resolvedAreaType = apiConfig.areaType || configFromMessage.areaType || "monotone";
            
                    const newSeriesConfigs: AreaSeriesConfig[] = resolvedYAxisKeys.map((key, index) => ({
                    dataKey: key,
                    name: resolvedAreaNames[index] || key,
                    color: resolvedColorsInput[index] || defaultAreaColors[index % defaultAreaColors.length],
                    type: resolvedAreaType as "monotone" | "linear" | "step" | "natural",
                    stackId: resolvedStackAreas ? 'stack1' : undefined,
                    gradientId: \`gradient-\${key.replace(/[^a-zA-Z0-9]/g, '-')}-\${index}\`,
                    strokeWidth: 2,
                    fillOpacity: 1,
                    }));
                    setSeriesConfigs(newSeriesConfigs);
            
                    const processedData = result.contents.map((item: any) => {
                    const newItem: AreaDataPoint = { ...item };
                    resolvedYAxisKeys.forEach(key => {
                        if (newItem[key] !== undefined && newItem[key] !== null) {
                        const val = parseFloat(String(newItem[key]).replace(',', '.'));
                        newItem[key] = isNaN(val) ? null : val;
                        } else {
                        newItem[key] = null;
                        }
                    });
                    if (resolvedXAxisKey && newItem[resolvedXAxisKey] !== undefined && newItem[resolvedXAxisKey] !== null) {
                        if (newItem[resolvedXAxisKey] instanceof Date) {
                        newItem[resolvedXAxisKey] = (newItem[resolvedXAxisKey] as Date).toLocaleDateString();
                        } else {
                        newItem[resolvedXAxisKey] = String(newItem[resolvedXAxisKey]);
                        }
                    }
                    return newItem;
                    });
                    setAreaData(processedData);
                    
                    setChartTitle(apiConfig.title || configFromMessage.title || "Gráfico de Área");
                    setChartDescription(apiConfig.description || configFromMessage.description || null);
                    setShowLegend(apiConfig.showLegend !== undefined ? apiConfig.showLegend : (configFromMessage.showLegend !== undefined ? configFromMessage.showLegend : true));
                    setStackAreas(resolvedStackAreas);
                    setAreaType(resolvedAreaType as "monotone" | "linear" | "step" | "natural");
                    setYAxisLabel(apiConfig.yAxisLabel || configFromMessage.yAxisLabel || null);
                    setXAxisLabel(apiConfig.xAxisLabel || configFromMessage.xAxisLabel || null);
                    setValueFormatOptions(apiConfig.valueFormatOptions || configFromMessage.valueFormatOptions || { minimumFractionDigits: 0, maximumFractionDigits: 1 });
                    setCurrentChartHeight(apiConfig.chartHeight || configFromMessage.chartHeight || "${initialChartHeight}");
            
                } else {
                    throw new Error("Dados inválidos ou em formato inesperado recebidos da query.");
                }
                } catch (err: any) {
                setError(err);
                setAreaData([]); 
                } finally {
                setIsLoading(false);
                }
            }, []); 
            
            useEffect(() => {
                setAreaData([]); 
                fetchData();
            }, [fetchData]); 
            
            const chartConfigForShadcn = useMemo(() => {
                const config: Record<string, { label: string; color: string; icon?: React.ElementType }> = {};
                seriesConfigs.forEach(series => {
                config[series.dataKey] = {
                    label: series.name,
                    color: series.color,
                };
                });
                return config;
            }, [seriesConfigs]);
            
             if (isLoading) {
                return (
                <div className='flex-grow flex flex-col items-center justify-center h-[300px] text-muted-foreground rounded'>
                        <LucideReact.Loader2 className="h-8 w-8 animate-spin" color="#333" />
                    </div>
                );
            }
            
            if (error) {
                return (
                <Card className="w-full h-[400px]">
                    <CardHeader>
                    <CardTitle className="text-destructive">Erro ao Carregar Gráfico</CardTitle>
                    <CardDescription className="text-destructive">
                        {error.message || "Ocorreu um erro desconhecido."}
                    </CardDescription>
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center h-full">
                        <LucideReact.AlertTriangle className="h-12 w-12 text-destructive mb-4"/>
                        <p>Não foi possível carregar os dados do gráfico.</p>
                    </CardContent>
                </Card>
                );
            }
            
            if (areaData.length === 0 && !isLoading) {
                return (
                <Card className="w-full h-[250px]">
                    <CardContent className="flex flex-col items-center justify-center h-[250px]">
                        <LucideReact.BarChart3 className="h-12 w-12 text-muted-foreground mb-4"/>
                        <p className="text-muted-foreground">Nenhum dado disponível para exibir o gráfico.</p>
                    </CardContent>
                </Card>
                );
            }
            
            return (
                <ChartContainer config={chartConfigForShadcn} className="h-full w-full p-4">
                    <ResponsiveContainer width="100%" height="100%">
                        <AreaChart
                        data={areaData}
                        margin={{
                            top: showLegend && seriesConfigs.length > 0 ? 5 : 10,
                            right: 15, 
                            left: yAxisLabel ? 5 : 25, 
                            bottom: xAxisLabel ? 20 : 5, 
                        }}
                        >
                        <defs>
                            {seriesConfigs.map((series) => (
                            <linearGradient
                                key={series.gradientId}
                                id={series.gradientId}
                                x1="0"
                                y1="0"
                                x2="0"
                                y2="1"
                            >
                                <stop offset="5%" stopColor={series.color} stopOpacity={0.6} />
                                <stop offset="95%" stopColor={series.color} stopOpacity={0.05} />
                            </linearGradient>
                            ))}
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" vertical={false} />
                        <XAxis
                            dataKey={xAxisKey!} 
                            stroke="hsl(var(--muted-foreground))"
                            fontSize={12}
                            tickLine={false}
                            axisLine={false}
                            tickMargin={8}
                            label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -15, fill: "hsl(var(--muted-foreground))", fontSize: 12 } : undefined}
                        />
                        <YAxis
                            stroke="hsl(var(--muted-foreground))"
                            fontSize={12}
                            tickLine={false}
                            axisLine={false}
                            tickMargin={8}
                            tickFormatter={formatValue}
                            label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', offset: -5, fill: "hsl(var(--muted-foreground))", fontSize: 12, dy:40 } : undefined}
                        />
                        <ChartTooltip
                            cursor={{ stroke: "hsl(var(--primary))", strokeWidth: 1, strokeDasharray: "3 3", fill: "hsl(var(--primary) / 0.1)" }}
                            content={
                            <ChartTooltipContent
                                indicator="dot"
                                labelClassName="font-semibold"
                                className="min-w-[150px] !p-2 shadow-lg rounded-md bg-popover text-popover-foreground border"
                                formatter={(value, name, itemPayload) => {
                                    const seriesConf = seriesConfigs.find(s => s.dataKey === itemPayload.dataKey);
                                    const displayName = seriesConf?.name || itemPayload.name || name;
                                    return [formatValue(value as number), displayName];
                                }}
                            />
                            }
                        />
                        {seriesConfigs.map((series) => (
                            <Area
                            key={series.dataKey}
                            type={series.type}
                            dataKey={series.dataKey}
                            name={series.name} 
                            stroke={series.strokeColor || series.color}
                            fillOpacity={series.fillOpacity}
                            strokeWidth={series.strokeWidth}
                            fill={\`url(#\${series.gradientId})\`}
                            stackId={series.stackId}
                            activeDot={{ r: 5, strokeWidth: 1, stroke: "hsl(var(--background))", fill: series.color }}
                            connectNulls={true} 
                            />
                        ))}
                        {showLegend && seriesConfigs.length > 0 && (
                            <Legend
                            verticalAlign="top" 
                            align="right"
                            wrapperStyle={{ top: -5, right: 0, zIndex: 10 }}
                            content={
                                <ChartLegendContent 
                                className="text-xs [&>button]:p-1 [&>button]:h-auto flex-wrap justify-end" 
                                nameKey="name" 
                                />
                            }
                            />
                        )}
                        </AreaChart>
                    </ResponsiveContainer>
                </ChartContainer>
            );
            }
        `;
    }

    function getTableWidgetShadcn(message: any) {
        return `
            import React, { useState, useEffect, useMemo, useCallback } from 'react';
            import {
                Table,
                TableBody,
                TableCell,
                TableHead,
                TableHeader,
                TableRow,
            } from "@/components/ui/table";
            import { Input } from "@/components/ui/input";
            import { Button } from "@/components/ui/button";
            import * as LucideReact from 'lucide-react';

            export function TableDemo() {
                const [tableData, setTableData] = useState([]);
                const [headers, setHeaders] = useState([]);
                const [isLoading, setIsLoading] = useState(true);
                const [error, setError] = useState(null);
                const [showErrorDetails, setShowErrorDetails] = useState(false);

                const [searchTerm, setSearchTerm] = useState('');
                const [sortConfig, setSortConfig] = useState({ key: null, direction: 'none' });

                useEffect(() => {
                const fetchData = async () => {
                    setIsLoading(true);
                    setError(null);
                    setShowErrorDetails(false);
                    setTableData([]);
                    setHeaders([]);
                    setSearchTerm('');
                    setSortConfig({ key: null, direction: 'none' });

                    try {
                    const query = '${message?.query ?? message[0]?.query ?? ''}';
                    if (!query) {
                        throw new Error("A query está vazia. Não é possível buscar dados.");
                    }
                    const result = await queryMitraWidget({ AuthorizationToken: "${TOKEN_LEGADO_MITRA}" }, query);
                    let newHeaders = [];
                    let newContents = [];
                    if (result && result.contents && Array.isArray(result.contents)) {
                        newContents = result.contents;
                        if (result.headers && Array.isArray(result.headers) && result.headers.length > 0) {
                        newHeaders = result.headers
                            .map((h, idx) => ({ name: String(h ?? \`Coluna \${idx + 1}\`) }))
                            .filter(header => header && typeof header.name === 'string' && header.name.trim() !== '');
                        } else if (newContents.length > 0 && newContents[0] && Array.isArray(newContents[0])) {
                        newHeaders = Array(newContents[0].length).fill(null).map((_, i) => ({ name: \`Coluna \${i + 1}\` }));
                        }
                    } else if (result && !result.contents) {
                        newContents = [];
                        if (result.headers && Array.isArray(result.headers) && result.headers.length > 0) {
                            newHeaders = result.headers
                            .map((h, idx) => ({ name: String(h ?? \`Coluna \${idx + 1}\`) }))
                            .filter(header => header && typeof header.name === 'string' && header.name.trim() !== '');
                        }
                    } else {
                        throw new Error("Dados inválidos ou em formato inesperado recebidos da query.");
                    }
                    setHeaders(newHeaders);
                    setTableData(newContents);
                    } catch (err) {
                    setError(err);
                    setTableData([]); setHeaders([]);
                    } finally {
                    setIsLoading(false);
                    }
                };
                fetchData();
                }, []);

                const handleSort = useCallback((columnIndex) => {
                if (!Array.isArray(headers) || headers.length === 0 || columnIndex < 0 || columnIndex >= headers.length) { return; }
                let direction = 'ascending';
                if (sortConfig.key === columnIndex) {
                    if (sortConfig.direction === 'ascending') direction = 'descending';
                    else if (sortConfig.direction === 'descending') direction = 'none';
                }
                setSortConfig({ key: columnIndex, direction });
                }, [headers, sortConfig]);

                const processedData = useMemo(() => {
                let dataToProcess = [...tableData];
                const currentHeaders = Array.isArray(headers) ? headers : [];
                if ( sortConfig.key !== null && typeof sortConfig.key === 'number' && sortConfig.direction !== 'none' && currentHeaders.length > 0 && sortConfig.key >= 0 && sortConfig.key < currentHeaders.length ) {
                    dataToProcess.sort((a, b) => {
                    const valA = (Array.isArray(a) && a.length > sortConfig.key) ? a[sortConfig.key] : undefined;
                    const valB = (Array.isArray(b) && b.length > sortConfig.key) ? b[sortConfig.key] : undefined;
                    if (valA === undefined && valB === undefined) return 0;
                    if (valA === undefined) return sortConfig.direction === 'ascending' ? 1 : -1;
                    if (valB === undefined) return sortConfig.direction === 'ascending' ? -1 : 1;
                    const strValA = String(valA); const strValB = String(valB);
                    const numA = parseFloat(strValA.replace(',', '.')); const numB = parseFloat(strValB.replace(',', '.'));
                    let comparison = 0;
                    if (!isNaN(numA) && !isNaN(numB)) comparison = numA - numB;
                    else comparison = strValA.toLowerCase().localeCompare(strValB.toLowerCase());
                    return sortConfig.direction === 'ascending' ? comparison : -comparison;
                    });
                }
                if (searchTerm) {
                    dataToProcess = dataToProcess.filter(row =>
                    Array.isArray(row) && row.some(cell =>
                        String(cell).toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    );
                }
                return dataToProcess;
                }, [tableData, searchTerm, sortConfig, headers]);
                
                const getColumnStyleInfo = (headerName, index, totalHeaders) => {
                const lowerHeaderName = String(headerName).toLowerCase();
                let widthClass = 'w-auto min-w-[120px]'; 
                let textAlignClass = 'text-left';

                if (lowerHeaderName.includes('valor') || lowerHeaderName.includes('preço') || 
                    lowerHeaderName.includes('total') || lowerHeaderName.includes('price') ||
                    lowerHeaderName.includes('qtd') || lowerHeaderName.includes('quantia') ||
                    lowerHeaderName.includes('número') || lowerHeaderName.includes('id') ||
                    lowerHeaderName.includes('código')) {
                    textAlignClass = 'text-right';
                    if (lowerHeaderName.includes('id') || lowerHeaderName.includes('código')) {
                    widthClass = 'w-[100px] min-w-[80px]';
                    } else {
                    widthClass = 'w-[150px] min-w-[100px]';
                    }
                }
                
                if (index === 0 && totalHeaders > 4) {
                }

                if (totalHeaders <= 3) {
                    widthClass = \`w-1/\${totalHeaders} min-w-[150px]\`;
                } else if (totalHeaders <= 5 && !widthClass.startsWith('w-[')) { 
                }


                return { widthClass, textAlignClass };
                };

                const safeHeaders = Array.isArray(headers) ? headers : [];
                
                if (isLoading) {
                return (
                    <div className='flex-grow flex flex-col items-center justify-center h-[300px] text-muted-foreground rounded'>
                            <LucideReact.Loader2 className="h-8 w-8 animate-spin" color="#333" />
                    </div>
                );
                }

                if (error) {
                return (
                    <div className='flex-grow flex flex-col items-center justify-center h-full min-h-[300px] text-red-600 bg-red-50 border border-red-200 rounded p-4 text-center'>
                        <LucideReact.AlertTriangle className="h-10 w-10 mb-3" />
                        <p className="font-semibold text-lg">Erro ao carregar dados</p>
                        <p className="text-sm text-red-500 mt-1 max-w-md">
                            {error.message || 'Ocorreu um erro desconhecido. Verifique o console para mais informações.'}
                        </p>
                        <div className="mt-5 space-x-2">
                            <Button onClick={() => window.location.reload()} variant="destructive" size="sm">
                                <LucideReact.RefreshCw className="mr-2 h-4 w-4" />
                                Tentar Novamente
                            </Button>
                            {error.stack && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setShowErrorDetails(!showErrorDetails)}
                                >
                                    {showErrorDetails ? <LucideReact.EyeOff className="mr-2 h-4 w-4" /> : <LucideReact.Eye className="mr-2 h-4 w-4" />}
                                    {showErrorDetails ? 'Ocultar Detalhes' : 'Ver Detalhes'}
                                </Button>
                            )}
                        </div>
                        {showErrorDetails && error.stack && (
                            <pre className="mt-4 p-3 bg-red-100 text-xs text-left overflow-auto max-h-60 w-full max-w-2xl border border-red-300 rounded">
                                {error.stack}
                            </pre>
                        )}
                    </div>
                );
                }

                if (safeHeaders.length === 0 && !isLoading) {
                return (
                    <div className='flex-grow flex flex-col items-center justify-center h-[300px] text-muted-foreground rounded p-4 text-center'>
                        <LucideReact.Info className="h-10 w-10 mb-3" />
                        <p className="text-lg">Nenhum dado para exibir</p>
                        <p className="text-sm text-gray-500">A consulta não retornou colunas ou dados.</p>
                    </div>
                );
                }

                return (
                <div className="p-4 bg-card rounded-lg shadow-md w-full h-full flex flex-col">
                    <div className="mb-4 flex items-center justify-between gap-2">
                    <Input
                        type="text"
                        placeholder={\`Buscar em \${tableData.length} registros...\`}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-md h-9 text-sm focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-primary dark:focus-visible:border-primary"
                        aria-label="Campo de busca na tabela"
                    />
                    </div>
                    <div className="flex-grow overflow-auto border border-border rounded-md">
                    <div className="table-container">
                        <Table className="w-full text-xs table-fixed">
                            <TableHeader className="sticky top-0 z-10 bg-muted/90 backdrop-blur-sm border-b border-border">
                            <TableRow className="h-10">
                                {safeHeaders.map((header, index) => {
                                const { widthClass, textAlignClass } = getColumnStyleInfo(header?.name, index, safeHeaders.length);
                                return (
                                    <TableHead
                                    key={(header?.name || \`header-\${index}\`) + '-' + index}
                                    onClick={() => handleSort(index)}
                                    className={\`px-3 py-2 font-medium text-muted-foreground cursor-pointer whitespace-nowrap \${widthClass} \${textAlignClass}\`}
                                    title={\`Ordenar por \${header?.name || ''}\`}
                                    >
                                    <div className={\`flex items-center \${textAlignClass === 'text-right' ? 'justify-end' : 'justify-start'}\`}>
                                        <span>{header?.name || \`Coluna \${index + 1}\`}</span>
                                        <span className="inline-flex">
                                        {sortConfig.key === index && sortConfig.direction === 'ascending' && (
                                            <LucideReact.ArrowUp className="ml-1.5 h-3.5 w-3.5 text-primary" />
                                        )}
                                        {sortConfig.key === index && sortConfig.direction === 'descending' && (
                                            <LucideReact.ArrowDown className="ml-1.5 h-3.5 w-3.5 text-primary" />
                                        )}
                                        {(sortConfig.key !== index || (sortConfig.key === index && sortConfig.direction === 'none')) && (
                                            <LucideReact.ChevronsUpDown className="ml-1.5 h-3.5 w-3.5 opacity-40" />
                                        )}
                                        </span>
                                    </div>
                                    </TableHead>
                                );
                                })}
                            </TableRow>
                            </TableHeader>
                            <TableBody>
                            {processedData.length > 0 ? (
                                processedData.map((row, rowIndex) => (
                                <TableRow
                                    key={rowIndex}
                                    className={\`h-9 \${rowIndex % 2 === 0 ? "bg-background" : "bg-muted/40"} hover:bg-accent/70 transition-colors duration-100\`}
                                >
                                    {Array.isArray(row) ? row.map((cell, cellIndex) => {
                                    const { textAlignClass } = getColumnStyleInfo(safeHeaders[cellIndex]?.name, cellIndex, safeHeaders.length);
                                    return (
                                        <TableCell
                                        key={cellIndex}
                                        className={\`py-1.5 px-3 truncate \${textAlignClass}\`}
                                        title={String(cell)}
                                        >
                                        {String(cell)}
                                        </TableCell>
                                    );
                                    }) : (
                                    <TableCell colSpan={safeHeaders.length || 1} className="text-destructive">
                                        Erro: Dados da linha inválidos.
                                    </TableCell>
                                    )}
                                </TableRow>
                                ))
                            ) : (
                                <TableRow className="h-24">
                                <TableCell colSpan={safeHeaders.length || 1} className="text-center text-muted-foreground">
                                    {searchTerm ? "Nenhum resultado encontrado para sua busca." : "Não há registros para exibir."}
                                </TableCell>
                                </TableRow>
                            )}
                            </TableBody>
                        </Table>
                    </div>
                    </div>
                </div>
                );
            }
        `;
    }
    function getMapWidgetLeafletOpenStreetMaps(message: any) {
        const query = message?.query ?? message?.[0]?.query ?? '';
        const mapConfigInput = message?.mapConfig ?? message?.[0]?.mapConfig ?? {}; 
        const defaultInitialMapHeight = '500px'; // Definido fora para consistência
    
        return `
            import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
            import { MapContainer, TileLayer, Marker, Popup, useMap } from 'react-leaflet';
            import L from 'leaflet';
            import 'leaflet/dist/leaflet.css';
    
            import {
              Card,
              CardContent,
              CardDescription,
              CardHeader,
              CardTitle,
            } from "@/components/ui/card";
            import * as LucideReact from 'lucide-react';
    
            delete (L.Icon.Default.prototype as any)._getIconUrl;
            L.Icon.Default.mergeOptions({
              iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
              iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
              shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
            });
    
            type MapDataPoint = Record<string, any> & {
              _lat?: number;
              _lon?: number;
              _geocodedLocationString?: string; 
              _geocodingError?: string;
              _originalIndex?: number; 
            };
            
            const LOCATION_KEYWORDS_PT = ['cidade', 'estado', 'municipio', 'município', 'uf', 'local', 'localidade', 'endereco', 'endereço', 'bairro', 'pais', 'país', 'logradouro', 'cep'];
            const LOCATION_KEYWORDS_EN = ['city', 'state', 'county', 'province', 'country', 'location', 'address', 'district', 'region', 'street', 'zipcode', 'postalcode'];
            const ALL_LOCATION_KEYWORDS = [...LOCATION_KEYWORDS_PT, ...LOCATION_KEYWORDS_EN, 'lat', 'lon', 'latitude', 'longitude', 'coordenadas'];
    
            function MapRefresher() {
                const map = useMap();
                
                useEffect(() => {
                    const mapElement = map.getContainer();
                    const parentElement = mapElement.parentElement;
    
                    if (!parentElement) return;
    
                    const resizeObserver = new ResizeObserver(() => {
                        requestAnimationFrame(() => {
                            map.invalidateSize();
                        });
                    });
    
                    const initialInvalidateTimeout = setTimeout(() => {
                         requestAnimationFrame(() => {
                            map.invalidateSize();
                        });
                    }, 200); 
    
                    resizeObserver.observe(parentElement);
    
                    return () => {
                        clearTimeout(initialInvalidateTimeout);
                        if (parentElement) {
                            resizeObserver.unobserve(parentElement);
                        }
                        resizeObserver.disconnect();
                    };
                }, [map]);
    
                return null;
            }
    
    
            function FitBoundsToMarkers({ markers }) {
              const map = useMap();
              const markersRef = useRef(markers); 
    
              useEffect(() => {
                markersRef.current = markers;
              }, [markers]);
    
              useEffect(() => {
                const currentMarkers = markersRef.current;
                if (map && currentMarkers && currentMarkers.length > 0) {
                  const validMarkers = currentMarkers.filter(m => typeof m._lat === 'number' && typeof m._lon === 'number');
                  if (validMarkers.length > 0) {
                    const bounds = L.latLngBounds(validMarkers.map(m => [m._lat, m._lon]));
                    if (bounds.isValid()) {
                      const fitBoundsTimeout = setTimeout(() => {
                        map.fitBounds(bounds, { padding: [50, 50], maxZoom: 16, animate: true });
                      }, 250); 
                      return () => clearTimeout(fitBoundsTimeout);
                    } else if (validMarkers.length === 1) {
                       const singleMarkerTimeout = setTimeout(() => {
                        map.setView([validMarkers[0]._lat, validMarkers[0]._lon], 13, { animate: true });
                      }, 250);
                      return () => clearTimeout(singleMarkerTimeout);
                    }
                  }
                }
              }, [map]); 
              
              return null;
            }
    
            export function MapWidgetDemo() {
              const [rawData, setRawData] = useState<Record<string, any>[]>([]);
              const [geoLocatedData, setGeoLocatedData] = useState<MapDataPoint[]>([]);
              const [isLoading, setIsLoading] = useState(true);
              const [isGeocoding, setIsGeocoding] = useState(false);
              const [error, setError] = useState<Error | null>(null);
              const [detailedErrorMessage, setDetailedErrorMessage] = useState<string | null>(null);
              const [progress, setProgress] = useState(0);
              const mapInstanceRef = useRef<L.Map | null>(null);
    
              // mapConfigInput é garantido ser um objeto pela função geradora
              const passedInConfig = useMemo(() => (${JSON.stringify(mapConfigInput)}), []); 
    
              const [mapTitle, setMapTitle] = useState<string>(passedInConfig.title ?? "Mapa de Localizações");
              const [mapDescription, setMapDescription] = useState<string | null>(passedInConfig.description ?? null);
              const [currentMapHeight, setCurrentMapHeight] = useState<string>(passedInConfig.mapHeight || "${defaultInitialMapHeight}");
              const [locationColumnKey, setLocationColumnKey] = useState<string | null>(null); // Inferido/definido no useEffect
              const [latColumnKey, setLatColumnKey] = useState<string | null>(null); // Inferido/definido no useEffect
              const [lonColumnKey, setLonColumnKey] = useState<string | null>(null); // Inferido/definido no useEffect
              const [valueColumnKey, setValueColumnKey] = useState<string | null>(passedInConfig.valueColumnKey ?? null);
              const [popupTitleKey, setPopupTitleKey] = useState<string | null>(null); // Inferido/definido no useEffect
              
              const initialCenter = useMemo(() => passedInConfig.initialCenter ?? [-14.2350, -51.9253], [passedInConfig.initialCenter]);
              const initialZoom = useMemo(() => passedInConfig.initialZoom ?? 4, [passedInConfig.initialZoom]);
              
              const geocodeDelay = 1100; 
    
              const identifyColumns = useCallback((data: Record<string, any>[]) => {
                if (!data || data.length === 0) return { autoLatKey: null, autoLonKey: null, autoLocKey: null };
                
                const firstRow = data[0];
                const keys = Object.keys(firstRow);
                let autoLatKey = null;
                let autoLonKey = null;
                let autoLocKey = null;
    
                for (const key of keys) {
                    const lowerKey = key.toLowerCase();
                    const valueSample = firstRow[key];
                    const isNumericOrParsable = typeof valueSample === 'number' || (typeof valueSample === 'string' && !isNaN(parseFloat(String(valueSample).replace(',','.'))));
    
                    if ((lowerKey.includes('lat') && !lowerKey.includes('long') && !lowerKey.includes('relat') && !lowerKey.includes('plate')) || lowerKey === 'latitude') {
                         if (isNumericOrParsable) autoLatKey = key;
                    }
                    if (lowerKey.includes('lon') || lowerKey.includes('lng') || lowerKey === 'longitude') {
                        if (isNumericOrParsable) autoLonKey = key;
                    }
                }
                
                if (!(autoLatKey && autoLonKey)) {
                    for (const key of keys) {
                        const lowerKey = key.toLowerCase();
                        if (ALL_LOCATION_KEYWORDS.some(kw => lowerKey.includes(kw) && kw !== 'lat' && kw !== 'lon' && kw !== 'latitude' && kw !== 'longitude')) {
                            if (!autoLocKey || key.length < autoLocKey.length) { 
                                autoLocKey = key;
                            }
                        }
                    }
                }
                return { autoLatKey, autoLonKey, autoLocKey };
              }, []);
    
              const geocodeLocation = useCallback(async (locationString: string): Promise<{lat: number, lon: number} | null> => {
                if (!locationString || String(locationString).trim() === '') return null;
                try {
                  const response = await fetch(\`https://nominatim.openstreetmap.org/search?format=json&q=\${encodeURIComponent(locationString)}&limit=1&countrycodes=BR\`);
                  if (!response.ok) {
                    console.error(\`MapWidgetDebug: Nominatim API error for '\${locationString}': \${response.status}\`);
                    return null;
                  }
                  const data = await response.json();
                  if (data && data.length > 0) {
                    return { lat: parseFloat(data[0].lat), lon: parseFloat(data[0].lon) };
                  }
                  return null;
                } catch (e) {
                  console.error(\`MapWidgetDebug: Error geocoding '\${locationString}':\`, e);
                  return null;
                }
              }, []);
    
              const processAndGeocodeData = useCallback(async (dataToProcess: Record<string, any>[], resolvedLocKey: string | null, resolvedLatKey: string | null, resolvedLonKey: string | null) => {
                setIsGeocoding(true);
                setProgress(0);
                const geocodedResults: MapDataPoint[] = [];
                
                for (let i = 0; i < dataToProcess.length; i++) {
                  const item = dataToProcess[i];
                  const mapPoint: MapDataPoint = { ...item, _originalIndex: i };
                  
                  if (resolvedLatKey && resolvedLonKey && item[resolvedLatKey] != null && item[resolvedLonKey] != null) {
                      const lat = parseFloat(String(item[resolvedLatKey]).replace(',', '.'));
                      const lon = parseFloat(String(item[resolvedLonKey]).replace(',', '.'));
                      if (!isNaN(lat) && !isNaN(lon)) {
                          mapPoint._lat = lat;
                          mapPoint._lon = lon;
                          mapPoint._geocodedLocationString = \`Lat: \${lat.toFixed(4)}, Lon: \${lon.toFixed(4)}\`;
                      } else {
                          mapPoint._geocodingError = "Coordenadas Lat/Lon inválidas na linha.";
                      }
                  } else if (resolvedLocKey && item[resolvedLocKey] != null) {
                    const locationValue = String(item[resolvedLocKey]);
                    mapPoint._geocodedLocationString = locationValue; 
                    const coords = await geocodeLocation(locationValue);
                    if (coords) {
                      mapPoint._lat = coords.lat;
                      mapPoint._lon = coords.lon;
                    } else {
                      mapPoint._geocodingError = \`Não geocodificado: '\${locationValue}'\`;
                    }
                    if (i < dataToProcess.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, geocodeDelay));
                    }
                  } else {
                    mapPoint._geocodingError = "Dados de localização ausentes na linha.";
                  }
                  geocodedResults.push(mapPoint);
                  setProgress(Math.round(((i + 1) / dataToProcess.length) * 100));
                }
                setGeoLocatedData(geocodedResults);
                setIsGeocoding(false);
              }, [geocodeLocation, geocodeDelay]);
    
              const memoizedQuery = useMemo(() => \`${query}\`, []);
              const memoizedToken = useMemo(() => "${TOKEN_LEGADO_MITRA}", []);
              
              useEffect(() => {
                const loadData = async () => {
                  setIsLoading(true);
                  setError(null);
                  setDetailedErrorMessage(null);
                  setRawData([]);
                  setGeoLocatedData([]);
                  
                  const queryString = memoizedQuery; 
                  const token = memoizedToken;
                  const configFromMessage = passedInConfig; // Usa a configuração inicial passada
    
                  try {
                    if (!queryString) {
                      throw new Error("A query SQL está vazia. Não é possível buscar dados para o mapa.");
                    }
                    const result = await queryMitraWidget({ AuthorizationToken: token }, queryString);
                    const apiConfig = result?.config || {}; 
    
                    // Atualiza os estados com base na prioridade: API -> Configuração passada -> Default
                    setMapTitle(apiConfig.title ?? configFromMessage.title ?? "Mapa de Localizações");
                    setMapDescription(apiConfig.description ?? configFromMessage.description ?? null);
                    
                    const newMapHeight = apiConfig.mapHeight ?? configFromMessage.mapHeight ?? "${defaultInitialMapHeight}";
                    // Só atualiza currentMapHeight se realmente mudar para evitar re-renderizações desnecessárias
                    if (newMapHeight !== currentMapHeight) { 
                        setCurrentMapHeight(newMapHeight);
                    }
                    
                    // As chaves de coluna são atualizadas aqui, após os dados serem carregados e analisados
                    const explicitLocKey = apiConfig.locationColumnKey ?? configFromMessage.locationColumnKey;
                    const explicitLatKey = apiConfig.latColumnKey ?? configFromMessage.latColumnKey;
                    const explicitLonKey = apiConfig.lonColumnKey ?? configFromMessage.lonColumnKey;
                    
                    // Atualiza valueColumnKey com base na mesma lógica de prioridade
                    setValueColumnKey(apiConfig.valueColumnKey ?? configFromMessage.valueColumnKey ?? null);
    
                    let dataForProcessing: Record<string, any>[] = [];
    
                    if (result && result.contents && Array.isArray(result.contents)) {
                        if (result.headers && Array.isArray(result.headers) && result.contents.length > 0 && Array.isArray(result.contents[0])) {
                            dataForProcessing = result.contents.map(rowArray => {
                                const rowObject: Record<string, any> = {};
                                result.headers.forEach((header, index) => {
                                    rowObject[String(header)] = rowArray[index];
                                });
                                return rowObject;
                            });
                        } else if (result.contents.length > 0 && typeof result.contents[0] === 'object' && !Array.isArray(result.contents[0]) && result.contents[0] !== null) {
                            dataForProcessing = result.contents;
                        } else if (result.contents.length === 0) {
                            dataForProcessing = [];
                        } else {
                            throw new Error("Formato de 'contents' da queryMitraWidget não reconhecido ou inconsistente.");
                        }
    
                        if (dataForProcessing.length === 0) {
                            setRawData([]);
                            setGeoLocatedData([]);
                            setIsLoading(false);
                            return;
                        }
                        setRawData(dataForProcessing); 
                        setIsLoading(false); 
    
                        const { autoLatKey, autoLonKey, autoLocKey } = identifyColumns(dataForProcessing);
                        
                        const finalLocKey = explicitLocKey || autoLatKey;
                        const finalLatKey = explicitLatKey || autoLatKey;
                        const finalLonKey = explicitLonKey || autoLonKey;
    
                        setLocationColumnKey(finalLocKey);
                        setLatColumnKey(finalLatKey);
                        setLonColumnKey(finalLonKey);
    
                        let inferredPopupTitleKey = apiConfig.popupTitleKey ?? configFromMessage.popupTitleKey;
                        if (!inferredPopupTitleKey) {
                            inferredPopupTitleKey = finalLocKey || finalLatKey || (dataForProcessing[0] ? Object.keys(dataForProcessing[0])[0] : null);
                        }
                        setPopupTitleKey(inferredPopupTitleKey);
    
                        // Re-infer valueColumnKey se não foi definido explicitamente, agora que temos as outras chaves finais
                        if (!valueColumnKey && dataForProcessing.length > 0 && dataForProcessing[0]) {
                            const firstRowKeys = Object.keys(dataForProcessing[0]);
                            const newValueColumnKey = firstRowKeys.find(key =>
                                key !== finalLocKey && key !== finalLatKey && key !== finalLonKey && key !== inferredPopupTitleKey &&
                                (typeof dataForProcessing[0][key] === 'number' || (typeof dataForProcessing[0][key] === 'string' && !isNaN(parseFloat(String(dataForProcessing[0][key]).replace(',','.')))))
                            ) || null;
                            setValueColumnKey(newValueColumnKey);
                        }
    
    
                        if ((finalLatKey && finalLonKey) || finalLocKey) {
                            await processAndGeocodeData(dataForProcessing, finalLocKey, finalLatKey, finalLonKey);
                        } else {
                            let errorDetail = "Verifique os nomes das colunas nos seus dados ou forneça 'locationColumnKey' (para nomes de lugares como 'cidade, estado') ou 'latColumnKey' e 'lonColumnKey' (para coordenadas diretas) no mapConfig.";
                            errorDetail += \`\\n\\nDados da primeira linha (após tentativa de transformação): \${JSON.stringify(dataForProcessing[0] || {})}\`;
                            errorDetail += \`\\nColunas automáticas tentadas: lat=\${autoLatKey || 'N/A'}, lon=\${autoLonKey || 'N/A'}, loc=\${autoLocKey || 'N/A'}\`;
                            errorDetail += \`\\nConfiguração explícita: lat=\${explicitLatKey || 'N/A'}, lon=\${explicitLonKey || 'N/A'}, loc=\${explicitLocKey || 'N/A'}\`;
                            setDetailedErrorMessage(errorDetail);
                            throw new Error("Não foi possível identificar ou configurar uma coluna de localização ou coordenadas (lat/lon).");
                        }
    
                    } else {
                      throw new Error("Dados inválidos ou em formato inesperado recebidos da query.");
                    }
                  } catch (err: any) {
                    setError(err);
                    if (!detailedErrorMessage && err.message) {
                        setDetailedErrorMessage(err.message);
                    }
                    setIsLoading(false);
                    setIsGeocoding(false);
                  }
                };
                loadData();
              }, [identifyColumns, processAndGeocodeData, memoizedQuery, memoizedToken, passedInConfig, currentMapHeight, valueColumnKey]); // Adicionado valueColumnKey para re-inferir se necessário
    
              const validMarkers = useMemo(() => 
                geoLocatedData.filter(d => typeof d._lat === 'number' && typeof d._lon === 'number'),
                [geoLocatedData]
              );
              
              useEffect(() => {
                if (mapInstanceRef.current) {
                    const map = mapInstanceRef.current;
                    const timer = setTimeout(() => {
                        requestAnimationFrame(() => {
                            map.invalidateSize();
                        });
                    }, 300); 
                    return () => clearTimeout(timer);
                }
              }, [currentMapHeight, validMarkers.length]);
    
    
              if (isLoading) { /* ... JSX de Loading ... */ }
              if (isGeocoding) { /* ... JSX de Geocoding ... */ }
              if (error) { /* ... JSX de Erro ... */ }
              if (rawData.length > 0 && validMarkers.length === 0 && !isGeocoding) { /* ... JSX Sem Marcadores Válidos ... */ }
              if (rawData.length === 0 && !isLoading && !isGeocoding) { /* ... JSX Sem Dados ... */ }
    
                if (isLoading) {
                return (
                  <Card className="w-full" style={{ height: currentMapHeight }}>
                    <CardHeader>
                      {mapTitle && <CardTitle>{mapTitle}</CardTitle>}
                      {mapDescription && <CardDescription>{mapDescription}</CardDescription>}
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center h-[calc(100%-4rem)]">
                      <LucideReact.Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <p className="mt-2 text-sm text-muted-foreground">Carregando dados do mapa...</p>
                    </CardContent>
                  </Card>
                );
              }
    
              if (isGeocoding) {
                 return (
                  <Card className="w-full" style={{ height: currentMapHeight }}>
                    <CardHeader>
                      {mapTitle && <CardTitle>{mapTitle}</CardTitle>}
                      {mapDescription && <CardDescription>{mapDescription}</CardDescription>}
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center h-[calc(100%-4rem)]">
                      <LucideReact.MapPin className="h-8 w-8 animate-pulse text-primary mb-2" />
                      <p className="text-sm text-muted-foreground">Geocodificando localizações...</p>
                      <div className="w-full max-w-xs bg-muted rounded-full h-2.5 mt-3 overflow-hidden">
                        <div className="bg-primary h-2.5 rounded-full transition-all duration-300 ease-linear" style={{ width: \`\${progress}%\` }}></div>
                      </div>
                       <p className="text-xs text-muted-foreground mt-1">\${progress}% concluído (\${geoLocatedData.length}/\${rawData.length})</p>
                       <p className="text-xs text-muted-foreground mt-2">Isso pode levar alguns instantes, especialmente para muitos locais.</p>
                    </CardContent>
                  </Card>
                );
              }
    
              if (error) {
                return (
                  <Card className="w-full" style={{ height: currentMapHeight }}>
                    <CardHeader>
                      <CardTitle className="text-destructive flex items-center">
                        <LucideReact.AlertTriangle className="h-5 w-5 mr-2" />
                        Erro ao Carregar Mapa
                      </CardTitle>
                      <CardDescription className="text-destructive whitespace-pre-wrap">
                        {error.message || "Ocorreu um erro desconhecido."}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex flex-col items-start justify-center h-[calc(100%-6rem)] overflow-auto p-4">
                      <p className="text-sm mb-2">Não foi possível carregar os dados do mapa.</p>
                      {detailedErrorMessage && (
                        <details className="text-xs text-muted-foreground w-full">
                            <summary className="cursor-pointer hover:underline">Ver detalhes técnicos</summary>
                            <pre className="mt-2 p-2 bg-muted rounded text-xs whitespace-pre-wrap break-all">
                                {detailedErrorMessage}
                            </pre>
                        </details>
                      )}
                    </CardContent>
                  </Card>
                );
              }
    
              if (rawData.length > 0 && validMarkers.length === 0 && !isGeocoding) {
                return (
                  <Card className="w-full" style={{ height: currentMapHeight }}>
                    <CardHeader>
                      {mapTitle && <CardTitle>{mapTitle}</CardTitle>}
                      {mapDescription && <CardDescription>{mapDescription}</CardDescription>}
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center h-[calc(100%-4rem)] text-center">
                      <LucideReact.MapX className="h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">Nenhuma localização pôde ser geocodificada ou exibida no mapa.</p>
                      <p className="text-xs text-muted-foreground mt-1">Verifique os dados, as colunas de localização/coordenadas, ou a configuração do mapa.</p>
                      {geoLocatedData.filter(d => d._geocodingError).length > 0 && (
                         <details className="text-xs text-muted-foreground mt-2 w-full max-w-md">
                            <summary className="cursor-pointer hover:underline">Ver erros de geocodificação (\${geoLocatedData.filter(d => d._geocodingError).length})</summary>
                            <ul className="mt-1 p-2 bg-muted rounded text-left max-h-32 overflow-y-auto">
                                {geoLocatedData.filter(d => d._geocodingError).slice(0,10).map((d,i) => (
                                    <li key={i} className="truncate" title={d._geocodingError}>- {d._geocodingError}</li>
                                ))}
                                {geoLocatedData.filter(d => d._geocodingError).length > 10 && <li>... e mais.</li>}
                            </ul>
                        </details>
                      )}
                    </CardContent>
                  </Card>
                );
              }
              
              if (rawData.length === 0 && !isLoading && !isGeocoding) {
                 return (
                  <Card className="w-full" style={{ height: currentMapHeight }}>
                    <CardHeader>
                      {mapTitle && <CardTitle>{mapTitle}</CardTitle>}
                      {mapDescription && <CardDescription>{mapDescription}</CardDescription>}
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center h-[calc(100%-4rem)]">
                      <LucideReact.SearchX className="h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">Nenhum dado encontrado para exibir no mapa.</p>
                    </CardContent>
                  </Card>
                );
              }
    
              return (
                <Card className="w-full overflow-hidden" style={{ height: currentMapHeight }}>
                  <CardHeader className="pb-2 pt-4">
                    {mapTitle && <CardTitle className="text-lg">{mapTitle}</CardTitle>}
                    {mapDescription && <CardDescription className="text-sm text-muted-foreground">{mapDescription}</CardDescription>}
                  </CardHeader>
                  <CardContent className="p-0 h-[calc(100%-3.5rem)]" style={{ position: 'relative', overflow: 'hidden' }}>
                    {typeof window !== 'undefined' && (
                        <MapContainer 
                            center={initialCenter as L.LatLngExpression} 
                            zoom={initialZoom} 
                            scrollWheelZoom={true} 
                            style={{ height: "100%", width: "100%" }}
                            className="rounded-b-lg"
                            whenCreated={(map) => { mapInstanceRef.current = map; }}
                        >
                        <TileLayer
                            attribution='© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                        />
                        {validMarkers.map((point, idx) => (
                            <Marker key={point._originalIndex ?? idx} position={[point._lat!, point._lon!]}>
                            <Popup minWidth={180}>
                                <div className="text-sm">
                                  {popupTitleKey && point[popupTitleKey] && (
                                    <strong className="block text-base mb-1">{String(point[popupTitleKey])}</strong>
                                  )}
                                  {(!popupTitleKey || (popupTitleKey && !point[popupTitleKey])) && point._geocodedLocationString && !(String(point._geocodedLocationString).startsWith("Lat:") && String(point._geocodedLocationString).includes("Lon:")) && (
                                     <strong className="block text-base mb-1">{String(point._geocodedLocationString)}</strong>
                                  )}
                                  {valueColumnKey && point[valueColumnKey] != null && (
                                    <p>
                                      <span className="font-semibold">{String(valueColumnKey).replace(/_/g, ' ')}: </span>
                                      {typeof point[valueColumnKey] === 'number' ? (point[valueColumnKey] as number).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) : String(point[valueColumnKey])}
                                    </p>
                                  )}
                                  {point._geocodedLocationString && 
                                   (!popupTitleKey || String(point[popupTitleKey]) !== point._geocodedLocationString) && 
                                   !(String(point._geocodedLocationString).startsWith("Lat:") && String(point._geocodedLocationString).includes("Lon:")) &&
                                   (
                                    <p className="text-xs text-muted-foreground mt-0.5">
                                      Localização original: {point._geocodedLocationString}
                                    </p>
                                  )}
                                  {Object.keys(point).filter(k => 
                                    !['_lat', '_lon', '_geocodedLocationString', '_geocodingError', '_originalIndex', locationColumnKey, valueColumnKey, popupTitleKey, latColumnKey, lonColumnKey].includes(k) && 
                                    point[k] != null && 
                                    String(point[k]).trim() !== '' &&
                                    String(point[k]).length < 70 
                                   ).slice(0,3).map(key => (
                                    <p key={key} className="text-xs mt-0.5">
                                        <span className="font-medium capitalize">{key.replace(/_/g, ' ')}: </span>
                                        {typeof point[key] === 'number' ? (point[key] as number).toLocaleString() : String(point[key])}
                                    </p>
                                  ))}
                                </div>
                            </Popup>
                            </Marker>
                        ))}
                        <FitBoundsToMarkers markers={validMarkers} />
                        <MapRefresher />
                        </MapContainer>
                    )}
                  </CardContent>
                </Card>
              );
            }
        `;
    }

    return {
        getBarWidgetShadcn,
        getPieWidgetShadcn,
        getTableWidgetShadcn,
        getAreaChartShadcn,
        getMapWidgetLeafletOpenStreetMaps
    };
}