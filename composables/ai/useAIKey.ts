export function useAIKey() {
	const localUseAIKey = ref(false);
	const savingUseAIKey = ref(false);
	const { userIsCorpAndUseOwnKey, hasWorkspaceKey, workspaceAISettings } =
		storeToRefs(useSuperAIStore());

	const { selectedWorkspace } = storeToRefs(useWorkspaceStore());

	async function saveUseAIKey(value: boolean) {
		savingUseAIKey.value = true;
		try {
			try {
				await useSuperAIStore().changeConsumptionAIMode(value);
				localUseAIKey.value = value;
			} catch (error) {
				// eslint-disable-next-line no-console
				console.log('error', error);
			} finally {
				savingUseAIKey.value = false;
			}
		} catch (error) {
			// eslint-disable-next-line no-console
			console.log('error', error);
		} finally {
			savingUseAIKey.value = false;
		}
	}

	function setLocalUseAIKey() {
		if (
			useWhiteLabel().isSankhyaClient &&
			workspaceAISettings.value?.fallbackAIKey &&
			!userIsCorpAndUseOwnKey.value
		) {
			return;
		}

		localUseAIKey.value = userIsCorpAndUseOwnKey.value;
	}

	setLocalUseAIKey();

	watch(
		() => selectedWorkspace.value,
		() => {
			setLocalUseAIKey();
		}
	);

	return {
		saveUseAIKey,
		savingUseAIKey,
		localUseAIKey,
		hasWorkspaceKey
	};
}
