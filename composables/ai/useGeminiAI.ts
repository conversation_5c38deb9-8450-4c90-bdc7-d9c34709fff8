/* eslint-disable no-console */
import { GoogleGenAI, Type } from '@google/genai';
import { useAnswerStreamProcessor } from './useAnswerStreamProcessor';

export default function useGeminiAI() {
	const ai = new GoogleGenAI({
		apiKey: useSuperAIStore().getAIKeyByPlan() || ''
	});

	const aiChatStore = useAiChatWithYourDataStore();
	const thoughtsStream = ref([] as { type: string; text: string }[]);
	const answerStream = ref([] as { type: string; text: string }[]);
	const answerProcessor = useAnswerStreamProcessor();

	const cleanContents = (contents: any[]) => {
		console.log('🧹 CLEAN CONTENTS - Input:', JSON.stringify(contents, null, 2));

		const cleaned = contents.map(content => ({
			...content,
			parts: content.parts?.filter((part: any) => {
				// Verifica se o part existe e não é um objeto vazio
				const isValid = part &&
					typeof part === 'object' &&
					Object.keys(part).length > 0 &&
					// Verifica se tem pelo menos uma propriedade com valor não vazio
					Object.values(part).some(value =>
						value !== null &&
						value !== undefined &&
						value !== '' &&
						(typeof value !== 'object' || Object.keys(value as any).length > 0)
					);

				if (!isValid) {
					console.log('🧹 CLEAN CONTENTS - Removing empty part:', JSON.stringify(part));
				}

				return isValid;
			}) || []
		})).filter(content => {
			const hasValidParts = content.parts.length > 0;
			if (!hasValidParts) {
				console.log('🧹 CLEAN CONTENTS - Removing content with no valid parts:', JSON.stringify(content));
			}
			return hasValidParts;
		});

		console.log('🧹 CLEAN CONTENTS - Output:', JSON.stringify(cleaned, null, 2));
		return cleaned;
	};

	// Throttle para processamento de chunks
	let pendingChunks: string[] = [];
	let throttleTimeout: NodeJS.Timeout | null = null;
	const THROTTLE_DELAY = 300; // 100ms de delay para agrupar chunks

	const processChunksThrottled = () => {
		if (pendingChunks.length === 0) return;

		// Junta todos os chunks pendentes em um único texto
		const combinedText = pendingChunks.join('');
		pendingChunks = [];

		// Processa o texto combinado para extrair objetos válidos
		const newValidObjects = answerProcessor.processChunk(combinedText);

		// Se encontrou novos objetos válidos, adiciona ao store
		if (newValidObjects.length > 0) {
			newValidObjects.forEach((obj) => {
				aiChatStore.addValidAnswerObject(obj);
			});
		}
	};

	const addChunkToProcess = (text: string) => {
		pendingChunks.push(text);

		// Cancela o timeout anterior se existir
		if (throttleTimeout) {
			clearTimeout(throttleTimeout);
		}

		// Agenda o processamento após o delay
		throttleTimeout = setTimeout(processChunksThrottled, THROTTLE_DELAY);
	};

	const { merge } = storeToRefs(useAuthStore());
	const TOKEN_LEGADO_MITRA = `Bearer ${merge?.value?.userToken}`;

	// Implementação da função run_sql
	const runSql = async (params: {
		query: string;
		jdbcConfigId?: number;
		baseUrl?: string;
		AuthorizationToken?: string;
	}) => {
		const {
			query,
			jdbcConfigId = 1,
			baseUrl = merge?.value?.backURL ?? 'https://api0.mitraecp.com:1005',
			AuthorizationToken = TOKEN_LEGADO_MITRA
		} = params;

		try {
			// Make the API request
			const response = await fetch(`${baseUrl}/iaShortcuts/query`, {
				method: 'POST',
				headers: {
					Accept: 'application/json, text/plain, */*',
					Authorization: AuthorizationToken,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ query, jdbcConfigId })
			});

			if (!response.ok) {
				let errorBody = null;
				let errorMessage = `HTTP ${response.status} - ${response.statusText}`;

				try {
					// Tenta capturar o corpo da resposta de erro
					const errorText = await response.text();
					if (errorText) {
						try {
							// Tenta parsear como JSON
							errorBody = JSON.parse(errorText);
							errorMessage = errorBody.message || errorBody.error || errorMessage;
						} catch {
							// Se não for JSON válido, usa o texto diretamente
							errorMessage = errorText;
						}
					}
				} catch (parseError) {
					console.warn('Could not parse error response:', parseError);
				}

				return {
					success: false,
					error: errorMessage,
					statusCode: response.status,
					statusText: response.statusText,
					errorBody,
					query,
					jdbcConfigId
				};
			}

			const data = await response.json();
			return {
				success: true,
				data,
				query,
				jdbcConfigId
			};
		} catch (error) {
			console.error('Error executing SQL query:', error);

			// Captura diferentes tipos de erro
			let errorMessage = 'Unknown error';
			let errorDetails = null;

			if (error instanceof Error) {
				errorMessage = error.message;
				errorDetails = {
					name: error.name,
					stack: error.stack
				};
			} else if (typeof error === 'string') {
				errorMessage = error;
			} else {
				errorDetails = error;
			}

			return {
				success: false,
				error: errorMessage,
				errorDetails,
				query,
				jdbcConfigId,
				errorType: 'network_or_parsing_error'
			};
		}
	};

	// Helper function to convert file to Gemini-compatible format
	// eslint-disable-next-line require-await
	async function fileToGenerativePart(
		file: File
	): Promise<{ inlineData: { data: string; mimeType: string } }> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onload = () => {
				const base64Data = (reader.result as string).split(',')[1]; // Remove data:image/jpeg;base64, prefix
				resolve({
					inlineData: {
						data: base64Data,
						mimeType: file.type
					}
				});
			};
			reader.onerror = reject;
			reader.readAsDataURL(file);
		});
	}

	async function generateGeminiResponse(
		{
			systemInstruction,
			userPrompt,
			customInstructions,
			model,
			googleSearchGrounding = false,
			codeExecutionEnabled = false
		}: {
			systemInstruction: string;
			userPrompt: [
				{
					role: string;
					parts: { text: string }[];
				}
			];
			customInstructions: string;
			model: string;
			googleSearchGrounding?: boolean;
			codeExecutionEnabled?: boolean;
		},
		uploadedFiles?: File[] | File | null
	) {
		try {
			// Limpa os streams antes de iniciar uma nova geração
			thoughtsStream.value = [];
			answerStream.value = [];
			aiChatStore.clearThoughtsStream();
			aiChatStore.clearAnswerStream();
			aiChatStore.clearValidAnswerObjects();
			answerProcessor.clearAccumulated();

			// Limpa o throttle pendente
			if (throttleTimeout) {
				clearTimeout(throttleTimeout);
				throttleTimeout = null;
			}
			pendingChunks = [];

			let thoughts = '';
			let answer = '';

			// Handle file upload for multimodal content
			const enhancedUserPrompt = [...userPrompt];

			if (uploadedFiles) {
				// Normalize to array (handle both single file and array of files)
				const filesArray = Array.isArray(uploadedFiles)
					? uploadedFiles
					: [uploadedFiles];

				// Convert all files to base64 for inline data
				const fileDataArray = await Promise.all(
					filesArray.map((file) => fileToGenerativePart(file))
				);

				// Enhance the last user message with all files
				const lastUserMessage = enhancedUserPrompt[enhancedUserPrompt.length - 1];
				if (lastUserMessage && lastUserMessage.role === 'user') {
					// Cast to any to handle multimodal content
					(lastUserMessage.parts as any[]) = [...lastUserMessage.parts, ...fileDataArray];
				}
			}

			const contents = [
				{ role: 'model', parts: [{ text: customInstructions }] },
				...enhancedUserPrompt
			];

			// Build tools array based on enabled features
			// Tool exclusivity rules:
			// 1. Code Execution + Google Search Grounding = Compatible ✅
			// 2. Code Execution + Function Calling (SQL) = Incompatible ❌
			// 3. Google Search Grounding + Function Calling (SQL) = Incompatible ❌
			const tools: any[] = [];

			// Add Google Search grounding if enabled
			if (googleSearchGrounding) {
				tools.push({
					googleSearch: {}
				});
				console.log('🔍 Google Search grounding enabled');
			}

			// Add Code Execution if enabled (compatible with Google Search)
			if (codeExecutionEnabled) {
				tools.push({
					codeExecution: {}
				});
				console.log('🐍 Code Execution enabled');
			}

			// Add SQL function calling only if neither Google Search nor Code Execution is enabled
			if (!googleSearchGrounding && !codeExecutionEnabled) {
				tools.push({
					functionDeclarations: [
						{
							name: 'run_sql',
							description:
								'Execute a SQL query and return the results to know if the query is valid.',
							parameters: {
								type: Type.OBJECT,
								properties: {
									query: {
										type: Type.STRING,
										description: 'The SQL query to execute, e.g. SELECT * FROM users WHERE id = 1'
									},
									jdbcConfigId: {
										type: Type.NUMBER,
										description: 'The JDBC config ID to use for the query'
									}
								},
								required: ['query', 'jdbcConfigId']
							}
						}
					]
				});
				console.log('🗄️ SQL function calling enabled');
			} else if (googleSearchGrounding || codeExecutionEnabled) {
				console.log(
					'🚫 SQL function calling disabled (incompatible with Google Search or Code Execution)'
				);
			}

			const toolConfig = {
				systemInstruction,
				temperature: 0.01,
				tools,
				thinkingConfig: {
					// thinkingBudget: 0.0001,
					includeThoughts: true
				}
			};

			// Primeira chamada com stream para capturar thinking e detectar function calls
			const initialResponse = await ai.models.generateContentStream({
				model,
				contents: cleanContents(contents),
				config: toolConfig
			});

			const functionCalls: any[] = [];
			let initialThoughts = '';
			let initialAnswer = '';
			const modelParts: any[] = [];
			let groundingMetadata = null;

			// Processa o stream inicial para capturar thinking e function calls
			for await (const chunk of initialResponse) {
				if (!chunk?.candidates?.[0]?.content?.parts) {
					console.warn('🚫 Invalid chunk structure:', chunk);
					continue;
				}

				// Verifica se as parts não são vazias
				const chunkParts = chunk.candidates[0].content.parts;
				if (!chunkParts || (Array.isArray(chunkParts) && chunkParts.length === 0)) {
					console.warn('🚫 Empty parts in chunk:', chunk);
					continue;
				}

				// Capture grounding metadata if available
				if (
					chunk.candidates &&
					chunk.candidates[0] &&
					chunk.candidates[0].groundingMetadata
				) {
					groundingMetadata = chunk.candidates[0].groundingMetadata;
					console.log(
						'🔍 GEMINI API - Initial grounding metadata captured:',
						groundingMetadata
					);
				}

				const parts = Array.isArray(chunk.candidates[0].content.parts)
					? chunk.candidates[0].content.parts
					: [chunk.candidates[0].content.parts];

				for (const part of parts) {
					// Verifica se a parte não é vazia antes de processar
					if (!part || typeof part !== 'object' || Object.keys(part).length === 0) {
						console.warn('🚫 Skipping empty part:', part);
						continue;
					}

					// Verifica se tem pelo menos uma propriedade com valor válido
					const hasValidContent = Object.values(part).some(value =>
						value !== null &&
						value !== undefined &&
						value !== '' &&
						(typeof value !== 'object' || Object.keys(value as any).length > 0)
					);

					if (!hasValidContent) {
						console.warn('🚫 Skipping part with no valid content:', part);
						continue;
					}

					console.log('🔍 Processing part:', {
						hasThought: !!part.thought,
						hasText: !!part.text,
						hasFunctionCall: !!part.functionCall,
						hasExecutableCode: !!part.executableCode,
						hasCodeExecutionResult: !!part.codeExecutionResult,
						googleSearchGrounding,
						codeExecutionEnabled
					});

					// Captura function calls
					if (part.functionCall) {
						functionCalls.push(part.functionCall);
						modelParts.push(part);
					} else if (part.executableCode) {
						// Process generated code from Code Execution
						console.log('🐍 Code generated:', part.executableCode.code);
						thoughtsStream.value.push({
							type: 'code-generated',
							text: JSON.stringify({
								code: part.executableCode.code,
								language: part.executableCode.language || 'python',
								timestamp: new Date().toISOString()
							})
						});
						aiChatStore.updateThoughtsStream(thoughtsStream.value);
						modelParts.push(part);
					} else if (part.codeExecutionResult) {
						// Process code execution results
						console.log('📊 Code execution result:', part.codeExecutionResult.output);
						thoughtsStream.value.push({
							type: 'code-executed',
							text: JSON.stringify({
								output: part.codeExecutionResult.output,
								outcome: part.codeExecutionResult.outcome,
								timestamp: new Date().toISOString()
							})
						});
						aiChatStore.updateThoughtsStream(thoughtsStream.value);
						modelParts.push(part);
					} else if (part.thought && part.text) {
						// Processa thinking em tempo real
						if (!initialThoughts) {
							console.log('🧠 Thoughts summary (with thought flag):');
						}
						console.log('🧠', part.text);
						initialThoughts = initialThoughts + part.text;
						thoughtsStream.value.push({
							type: 'thought',
							text: part.text
						});
						aiChatStore.updateThoughtsStream(thoughtsStream.value);
						modelParts.push(part);
					} else if (part.text) {
						// FIXED: When Google Search grounding is enabled, treat all text as answer
						// The thinking detection was incorrectly categorizing answer text as thinking
						console.log('💬 Answer part:', part.text);
						initialAnswer = initialAnswer + part.text;
						answerStream.value.push({
							type: 'answer',
							text: part.text
						});

						// Adiciona o chunk ao processamento throttled
						addChunkToProcess(part.text);

						aiChatStore.updateAnswerStream(answerStream.value);
						modelParts.push(part);
					}
				}
			}

			// Adiciona a resposta inicial do modelo ao histórico se houver partes válidas
			if (modelParts.length > 0) {
				// Filtra partes vazias antes de adicionar ao contents
				const validParts = modelParts.filter(part =>
					part &&
					typeof part === 'object' &&
					Object.keys(part).length > 0 &&
					Object.values(part).some(value =>
						value !== null &&
						value !== undefined &&
						value !== '' &&
						(typeof value !== 'object' || Object.keys(value as any).length > 0)
					)
				);

				if (validParts.length > 0) {
					console.log('🔄 Adding initial model parts to contents:', validParts.length, 'valid parts');
					contents.push({
						role: 'model',
						parts: validParts as { text: string }[]
					});
				} else {
					console.log('🚫 Skipping empty model parts for initial response');
				}
			}

			// Loop infinito para processar function calls até que não haja mais
			let currentFunctionCalls = functionCalls;
			let iterationCount = 0;
			const maxIterations = 10; // Limite de segurança para evitar loops infinitos

			while (
				currentFunctionCalls &&
				currentFunctionCalls.length > 0 &&
				iterationCount < maxIterations
			) {
				iterationCount++;
				console.log(
					`Function calls iteration ${iterationCount}:`,
					currentFunctionCalls
				);

				// Executa cada function call da iteração atual
				for (const functionCall of currentFunctionCalls) {
					if (functionCall.name === 'run_sql') {
						try {
							const args = functionCall.args as { query: string; jdbcConfigId: number };
							const sqlResult = await runSql({
								query: args?.query || '',
								jdbcConfigId: args?.jdbcConfigId || 1
							});

							// Adiciona ao stream como tool execution
							const toolExecutionData = {
								functionName: functionCall.name,
								query: args?.query || '',
								jdbcConfigId: args?.jdbcConfigId || 1,
								result: sqlResult,
								timestamp: new Date().toISOString(),
								success: sqlResult.success
							};

							thoughtsStream.value.push({
								type: 'tool-run_sql',
								text: JSON.stringify(toolExecutionData)
							});

							// Atualiza o store com o novo valor do thoughts stream
							aiChatStore.updateThoughtsStream(thoughtsStream.value);

							// Adiciona o resultado da function call ao histórico
							contents.push({
								role: 'user',
								parts: [
									{
										text: `Function ${functionCall.name} result: ${JSON.stringify(sqlResult)}`
									}
								]
							});

							console.log('SQL function executed:', args?.query, 'Result:', sqlResult);
						} catch (error) {
							console.error('Error executing SQL function:', error);

							// Adiciona ao stream como tool execution com erro
							const args = functionCall.args as { query: string; jdbcConfigId: number };
							const toolExecutionErrorData = {
								functionName: functionCall.name,
								query: args?.query || '',
								jdbcConfigId: args?.jdbcConfigId || 1,
								error,
								timestamp: new Date().toISOString(),
								success: false
							};

							thoughtsStream.value.push({
								type: 'tool-run_sql',
								text: JSON.stringify(toolExecutionErrorData)
							});

							// Atualiza o store com o novo valor do thoughts stream
							aiChatStore.updateThoughtsStream(thoughtsStream.value);

							// Cria uma mensagem de erro detalhada para o modelo
							let errorMessage = `Function ${functionCall.name} failed to execute.`;

							if (error && typeof error === 'object') {
								const errorObj = error as any;

								if (errorObj.statusCode) {
									errorMessage += ` HTTP ${errorObj.statusCode}`;
									if (errorObj.statusText) {
										errorMessage += ` - ${errorObj.statusText}`;
									}
								}

								if (errorObj.error) {
									errorMessage += `\nError: ${errorObj.error}`;
								}

								if (errorObj.errorBody) {
									errorMessage += `\nDetails: ${JSON.stringify(errorObj.errorBody, null, 2)}`;
								}

								if (errorObj.errorType) {
									errorMessage += `\nType: ${errorObj.errorType}`;
								}

								if (errorObj.query) {
									errorMessage += `\nQuery: ${errorObj.query}`;
								}
								if (errorObj.jdbcConfigId) {
									errorMessage += `\nJDBC Config ID: ${errorObj.jdbcConfigId}`;
								}
							} else if (error instanceof Error) {
								errorMessage += `\nError: ${error.message}`;
								if (error.stack) {
									errorMessage += `\nStack: ${error.stack}`;
								}
							} else {
								errorMessage += `\nError: ${String(error)}`;
							}

							// Adiciona o erro detalhado como resposta da function
							contents.push({
								role: 'user',
								parts: [
									{
										text: errorMessage
									}
								]
							});
						}
					}
				}

				// Faz nova chamada para verificar se há mais function calls
				const nextResponse = await ai.models.generateContentStream({
					model,
					contents: cleanContents(contents),
					config: toolConfig
				});

				// Processa a resposta para detectar novos function calls
				const nextResult = await processStreamResponseWithFunctionCalls(nextResponse);

				// Adiciona os novos thoughts e answer
				thoughts = initialThoughts + nextResult.thoughts;
				answer = initialAnswer + nextResult.answer;

				// Update grounding metadata if available from subsequent calls
				if (nextResult.groundingMetadata) {
					groundingMetadata = nextResult.groundingMetadata;
				}

				// Adiciona as partes do modelo ao histórico se houver partes válidas
				if (nextResult.modelParts.length > 0) {
					// Filtra partes vazias antes de adicionar ao contents
					const validParts = nextResult.modelParts.filter(part =>
						part &&
						typeof part === 'object' &&
						Object.keys(part).length > 0 &&
						Object.values(part).some(value =>
							value !== null &&
							value !== undefined &&
							value !== '' &&
							(typeof value !== 'object' || Object.keys(value as any).length > 0)
						)
					);

					if (validParts.length > 0) {
						console.log('🔄 Adding subsequent model parts to contents:', validParts.length, 'valid parts');
						contents.push({
							role: 'model',
							parts: validParts as { text: string }[]
						});
					} else {
						console.log('🚫 Skipping empty model parts for subsequent response');
					}
				}

				// Atualiza para a próxima iteração
				currentFunctionCalls = nextResult.functionCalls;
			}

			// Se não houve function calls na primeira chamada, usa os resultados iniciais
			if (functionCalls.length === 0) {
				thoughts = initialThoughts;
				answer = initialAnswer;

				// Ensure grounding metadata is preserved when no function calls
				console.log(
					'🔍 No function calls - preserving grounding metadata:',
					groundingMetadata
				);
			}

			// Processa qualquer chunk pendente antes de finalizar
			if (throttleTimeout) {
				clearTimeout(throttleTimeout);
				throttleTimeout = null;
			}
			if (pendingChunks.length > 0) {
				processChunksThrottled();
			}

			// Validação: Verifica se retornou finishReason STOP mas só tem conteúdo de pensamento
			// No método principal, verifica se há function calls pendentes
			const hasPendingFunctionCalls = functionCalls.length > 0;
			validateGeminiResponse(thoughts, answer, hasPendingFunctionCalls, 'generateGeminiResponse');

			console.log('🔍 GEMINI API - Final return:', {
				thoughts: thoughts.length,
				answer: answer.length,
				groundingMetadata,
				hasGroundingMetadata: !!groundingMetadata
			});
			return { thoughts, answer, groundingMetadata };
		} catch (error) {
			console.error('Error generating Gemini response:', error);

			// Marca o erro como originário do Gemini para detecção no useLangFlowCaller
			const geminiError = error instanceof Error ? error : new Error(String(error));
			(geminiError as any).isGeminiError = true;
			(geminiError as any).geminiErrorSource = 'useGeminiAI.ts';

			throw geminiError;
		}
	}

	// Função auxiliar para processar o stream de resposta com detecção de function calls
	async function processStreamResponseWithFunctionCalls(response: any) {
		let thoughts = '';
		let answer = '';
		const functionCalls: any[] = [];
		const modelParts: any[] = [];
		let groundingMetadata = null;

		for await (const chunk of response) {
			// Ensure chunk and its properties exist before accessing
			if (!chunk?.candidates?.[0]?.content?.parts) {
				console.warn('Invalid chunk structure:', chunk);
				continue;
			}

			// Capture grounding metadata if available
			if (
				chunk.candidates &&
				chunk.candidates[0] &&
				chunk.candidates[0].groundingMetadata
			) {
				groundingMetadata = chunk.candidates[0].groundingMetadata;
				console.log('Grounding metadata captured:', groundingMetadata);
			}

			const parts = Array.isArray(chunk.candidates[0].content.parts)
				? chunk.candidates[0].content.parts
				: [chunk.candidates[0].content.parts];

			for (const part of parts) {
				// Verifica se a parte não é vazia antes de processar
				if (!part || typeof part !== 'object' || Object.keys(part).length === 0) {
					console.warn('🚫 Skipping empty subsequent part:', part);
					continue;
				}

				// Verifica se tem pelo menos uma propriedade com valor válido
				const hasValidContent = Object.values(part).some(value =>
					value !== null &&
					value !== undefined &&
					value !== '' &&
					(typeof value !== 'object' || Object.keys(value as any).length > 0)
				);

				if (!hasValidContent) {
					console.warn('🚫 Skipping subsequent part with no valid content:', part);
					continue;
				}

				console.log('🔍 Processing subsequent part:', {
					hasThought: !!part.thought,
					hasText: !!part.text,
					hasFunctionCall: !!part.functionCall,
					hasExecutableCode: !!part.executableCode,
					hasCodeExecutionResult: !!part.codeExecutionResult
				});

				// Captura function calls
				if (part.functionCall) {
					functionCalls.push(part.functionCall);
					modelParts.push(part);
				} else if (part.executableCode) {
					// Process subsequent generated code from Code Execution
					console.log('🐍 Subsequent code generated:', part.executableCode.code);
					thoughtsStream.value.push({
						type: 'code-generated',
						text: JSON.stringify({
							code: part.executableCode.code,
							language: part.executableCode.language || 'python',
							timestamp: new Date().toISOString()
						})
					});
					aiChatStore.updateThoughtsStream(thoughtsStream.value);
					modelParts.push(part);
				} else if (part.codeExecutionResult) {
					// Process subsequent code execution results
					console.log(
						'📊 Subsequent code execution result:',
						part.codeExecutionResult.output
					);
					thoughtsStream.value.push({
						type: 'code-executed',
						text: JSON.stringify({
							output: part.codeExecutionResult.output,
							outcome: part.codeExecutionResult.outcome,
							timestamp: new Date().toISOString()
						})
					});
					aiChatStore.updateThoughtsStream(thoughtsStream.value);
					modelParts.push(part);
				} else if (part.thought && part.text) {
					// Processa thinking em tempo real
					if (!thoughts) {
						console.log('🧠 Subsequent thoughts summary (with thought flag):');
					}
					console.log('🧠', part.text);
					thoughts = thoughts + part.text;
					thoughtsStream.value.push({
						type: 'thought',
						text: part.text
					});
					// Atualiza o store com o novo valor
					aiChatStore.updateThoughtsStream(thoughtsStream.value);
					modelParts.push(part);
				} else if (part.text) {
					// Processa resposta
					if (!answer) {
						console.log('💬 Subsequent answer:');
					}
					console.log('💬', part.text);
					answer = answer + part.text;
					answerStream.value.push({
						type: 'answer',
						text: part.text
					});

					// Adiciona o chunk ao processamento throttled
					addChunkToProcess(part.text);

					// Atualiza o store com o novo valor do answer stream
					aiChatStore.updateAnswerStream(answerStream.value);
					modelParts.push(part);
				}
			}
		}

		// Validação: Verifica se retornou finishReason STOP mas só tem conteúdo de pensamento
		// Este método sempre processa function calls, então verifica se há calls detectados
		const hasFunctionCallsDetected = functionCalls.length > 0;
		validateGeminiResponse(thoughts, answer, hasFunctionCallsDetected, 'processStreamResponseWithFunctionCalls');

		// console.log('🔍 processStreamResponseWithFunctionCalls - Final return:', { thoughts: thoughts.length, answer: answer.length, groundingMetadata });
		return { thoughts, answer, functionCalls, modelParts, groundingMetadata };
	}

	/**
	 * Valida se a resposta do Gemini contém apenas conteúdo de pensamento
	 * Lança erro para forçar retry quando necessário
	 * NÃO considera erro quando há functionCall (é esperado não ter answer ainda)
	 */
	const validateGeminiResponse = (thoughts: string, answer: string, hasFunctionCalls: boolean = false, context: string = '') => {
		// Se há function calls, é esperado não ter answer ainda (Gemini está esperando resultado da função)
		if (hasFunctionCalls) {
			console.log(`🔧 GEMINI API${context ? ` (${context})` : ''} - Has function calls, skipping thought-only validation`);
			return;
		}

		// Só considera erro se não há function calls E só tem pensamento
		if (answer.trim() === '' && thoughts.trim() !== '') {
			const contextSuffix = context ? ` (${context})` : '';
			console.warn(`🚨 GEMINI API${contextSuffix} - Detected STOP with only thought content, triggering retry`);

			const thoughtOnlyError = new Error(`Gemini returned finishReason STOP but only provided thought content without actual answer${contextSuffix}`);
			(thoughtOnlyError as any).isGeminiError = true;
			(thoughtOnlyError as any).geminiErrorSource = 'useGeminiAI.ts';
			(thoughtOnlyError as any).errorType = 'thought_only_response';
			(thoughtOnlyError as any).context = context;
			(thoughtOnlyError as any).geminiThoughts = thoughts; // Adiciona os pensamentos ao erro

			throw thoughtOnlyError;
		}

		// Verifica se a resposta está vazia (sem pensamento e sem resposta)
		if (answer.trim() === '' && thoughts.trim() === '') {
			const contextSuffix = context ? ` (${context})` : '';
			console.warn(`🚨 GEMINI API${contextSuffix} - Detected empty response, triggering retry`);

			const emptyResponseError = new Error(`Gemini returned empty response with no content${contextSuffix}`);
			(emptyResponseError as any).isGeminiError = true;
			(emptyResponseError as any).geminiErrorSource = 'useGeminiAI.ts';
			(emptyResponseError as any).errorType = 'empty_response';
			(emptyResponseError as any).context = context;

			throw emptyResponseError;
		}
	};

	const getDataInnerTagsOnText = (text: string, tag: string) => {
		const regex = new RegExp(`<${tag}>[\\s\\S]*?<\\/${tag}>`, 'g');
		const matches = text?.match(regex);
		return matches
			? matches[0]?.replace(`<${tag}>`, '').replace(`</${tag}>`, '')
			: '';
	};

	return {
		generateGeminiResponse,
		thoughtsStream,
		answerStream,
		answerProcessor,
		getDataInnerTagsOnText,
		runSql
	};
}
