export default function useEmbedded() {
	const getScreenId = ({ raw }: { raw?: boolean }) => {
		const query = useRoute().query;
		const queryScreenId = query.screenId as unknown as number;
		const queryWorkspaceId = query.workspaceId as unknown as number;

		if (queryScreenId && !isNaN(queryScreenId) && (!raw || !queryWorkspaceId)) {
			return queryScreenId;
		} else {
			return 0;
		}
	};

	return {
		getScreenId
	};
}
