
import useFetchApi from '../clients/useFetchApi';

export default function useLegacyService() {
	const { mergePayload } = useLegacyMerge();
	const getHtmlVersionList = async (id: number) => {
		const { data, pending, error } = await useFetchApi(`/htmlComponentVersion/${id}`, {
			...mergePayload({
				method: 'GET'
			})
		});
		return {
			data: data?.value as any,
			pending,
			error: error.value as CustomError
		};
	};

	return {
		getHtmlVersionList
	};
}
