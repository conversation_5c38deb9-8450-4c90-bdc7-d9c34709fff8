.Vue-Toastification {
	&__toast.large_toast {
		@apply rounded-md border-[1px] border-solid px-4 py-3;
		min-width: unset;
		min-height: unset;
		box-shadow: unset;
		&-component-body {
			& > .toast-container span {
				color: var(--grey-900);
			}
		}
	}
	&__toast:has(.success) {
		@apply border-success-700 bg-success-25 !text-success-700;
	}
	&__toast:has(.error) {
		@apply border-error-700 bg-error-25 !text-error-700;
	}
	&__toast:has(.info) {
		@apply border-primary-700 bg-primary-25 !text-primary-700;
	}
	&__toast:has(.warning) {
		@apply border-warning-700 bg-warning-25 !text-warning-700;
	}
	&__close-button {
		@apply text-grey-400;
	}
	&__icon {
		@apply hidden;
	}
}

.Vue-Toastification {
	&__toast.default_toast {
		@apply rounded-md bg-white px-4 py-3;
		min-width: unset;
		min-height: unset;
		box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05),
			0px 0px 0px 4px var(--grey-100, var(--violet-10));
	}
	&__close-button {
		@apply text-grey-400;
	}
	&__icon {
		@apply hidden;
	}
}
