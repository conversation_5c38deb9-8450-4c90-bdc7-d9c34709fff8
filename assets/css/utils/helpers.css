@layer utilities {
	.dev-mode .log {
		border: 1px solid red;
	}

	.bg-gradient {
		@apply bg-gradient-to-r from-purple to-blue;
	}

	.icon-wrapper {
		border-radius: 5.53846px;
		@apply mr-4 flex h-[24px] w-[24px] items-center justify-center bg-grey-200;
		&__mobile-icon {
			@apply h-[12.83px] w-[8.16px];
		}
		&__layout-icon {
			@apply h-[14px] w-[14px];
		}
	}

	.rounded-icon-button {
		@apply flex items-center rounded-full p-1 hover:bg-grey-50 cursor-pointer;
		&.disabled {
			opacity: 0.5;
			pointer-events: none;
		}
	}

	.absolute-center {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.btn-border {
		border: 1px solid var(--grey-200);
		@apply rounded-lg shadow-thin;
	}

	.bottom-line {
		border-bottom: 1px solid #eaecf0;
	}

	.centered {
		@apply flex items-center justify-center;
	}

	.noselect {
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		-khtml-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}

	.sm-border-all {
		border: 1px solid var(--grey-100);
	}

	.sm-border-t {
		border-top: 1px solid var(--grey-100);
	}

	.sm-border-b {
		border-bottom: 1px solid var(--grey-100);
	}

	.sm-border-l {
		border-left: 1px solid var(--grey-100);
	}

	.no-border-l {
		border-left: none;
	}

	.no-border-r {
		border-right: none;
	}

	.no-border-t {
		border-top: none;
	}

	.no-border-b {
		border-bottom: none;
	}

	.sm-border-r {
		border-right: 1px solid var(--grey-100);
	}

	.light-border-all {
		border: 1px solid var(--grey-200);
	}

	.light-border-t {
		border-top: 1px solid var(--grey-200);
	}

	.item-disabled {
		pointer-events: none;
		opacity: 0.3;
	}

	.base-title {
		@apply text-grey-900 font-medium text-sm leading-5;
	}

	.item-loading {
		opacity: 0.3;
		cursor: wait;
	}

	.light-border-b {
		border-bottom: 1px solid var(--grey-200);
	}

	.light-border-l {
		border-left: 1px solid var(--grey-200);
	}

	.light-border-r {
		border-right: 1px solid var(--grey-200);
	}

	.light-border-x {
		border-left: 1px solid var(--grey-200);
		border-right: 1px solid var(--grey-200);
	}

	.light-border-y {
		border-top: 1px solid var(--grey-200);
		border-bottom: 1px solid var(--grey-200);
	}

	.medium-border-all {
		border: 1px solid var(--grey-300);
	}

	.medium-border-t {
		border-top: 1px solid var(--grey-300);
	}

	.medium-border-b {
		border-bottom: 1px solid var(--grey-300);
	}

	.medium-border-l {
		border-left: 1px solid var(--grey-300);
	}

	.medium-border-r {
		border-right: 1px solid var(--grey-300);
	}

	.medium-border-x {
		border-left: 1px solid var(--grey-300);
		border-right: 1px solid var(--grey-300);
	}

	.medium-border-y {
		border-top: 1px solid var(--grey-300);
		border-bottom: 1px solid var(--grey-300);
	}

	.height-fill-available {
		height: -webkit-fill-available;
	}

	.width-fill-available {
		width: -webkit-fill-available;
	}

	.clean-svg {
		shape-rendering: optimizespeed;
	}

	.upgrade-tag {
		font-family: Inter;
		font-size: 12px;
		font-weight: 500;
		line-height: 18px;
		padding: 2px 8px 2px 8px;
		border-radius: 8px;
		background-color: var(--violet-100);
		color: var(--violet-600);
	}

	.enterprise-tag div {
		background: radial-gradient(108% 108% at 100% 0%, #23a9ff 0%, #005d9f 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}
	.enterprise-tag {
		font-family: Inter;
		font-size: 12px;
		font-weight: 500;
		line-height: 18px;
		padding: 2px 8px 2px 8px;
		border-radius: 8px;
		background-color: #eef8fe;
	}

	.enterprise-disabled:not(:has(.ignore-disabled)) {
		cursor: not-allowed;
	}

	.enterprise-disabled:not(:has(.ignore-disabled)) > div:not(:last-child) {
		opacity: 0.5;
	}
}
