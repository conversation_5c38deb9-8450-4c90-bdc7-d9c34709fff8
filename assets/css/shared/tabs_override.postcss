:deep(.custom-navigation-tabs .ant-tabs-nav) {
	margin-top: 0px !important;
}

:deep(.custom-navigation-tabs .ant-tabs-tab-active) {
	background: transparent !important;
}

:deep(.custom-navigation-tabs) {
	.ant-tabs-tab-btn {
		color: var(--grey-500) !important;
		font-family: Inter;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 20px;
		background: transparent !important;
	}

	.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--violet-700) !important;
		text-shadow: none;
	}

	.ant-tabs-content-holder {
		display: flex !important;
	}

	.ant-tabs-tabpane-active {
		display: flex;
	}

	.ant-tabs-tab-active {
		background-color: var(--primary-50);
	}

	.ant-tabs-tab + .ant-tabs-tab {
		margin: 0 0 0 12px;
	}

	.ant-tabs-nav {
		height: 36px;
		margin: 0px !important;
		&::before {
			border: none;
		}

		.ant-tabs {
			&-ink-bar {
				background: var(--violet-700) !important;
			}
			&-tab {
				color: var(--violet-600);
				border-radius: 6px;
				padding: 8px 12px;
				justify-content: center;
				font-weight: 500;
				&-btn {
					color: var(--primary-700);
				}
				&:not(.ant-tabs-tab-active):hover {
					color: var(--primary-700);
					.ant-tabs-tab-btn {
						color: var(--primary-700);
					}
				}
			}
		}
	}
}
:deep(.ant-tabs) {
	overflow: unset;
}
