:deep(.workspace-small-square) {
	@apply h-[20px] w-[20px];
}

:deep(.project-badge) {
	@apply !h-[26px] !min-h-[26px] !w-[26px] !min-w-[26px];
}

.subscribe-list :deep(.accordion-item) {
	padding: 0 8px;
}

.subscribe-list :deep(.toggle-icon-box) {
	svg {
		color: var(--grey-400) !important;
	}
}

.subscribe-list :deep(.accordion-item) {
	height: 40px;
	margin-bottom: 0;
}

.project-list :deep(.group-divider) {
	padding-left: 0 !important;
	border-left: unset !important;
	margin-left: 0 !important;
}

:deep(.accordion-item) {
	margin-bottom: 6px;
}

:deep(.toggle-icon-box) {
	svg {
		color: var(--grey-700);
	}
}

:deep(.content-icon__square-box) {
	margin: 0 !important;
}

.sidebar-wrapper {
	@apply flex flex-col px-6 py-6 smHeight:py-4;
}

.project {
	&-favorited {
		@apply my-3 flex justify-between smHeight:mb-0;
	}

	&-list__wrapper {
		@apply overflow-auto pb-2 pl-6 pr-3.5;
	}
}

.project-arrow {
	min-width: 20px;
	min-height: 20px;
}

.group-header {
	@apply my-6 flex flex-col items-center justify-between px-6 smHeight:my-3;
}

.quickly-access {
	@apply my-1 flex w-[70%] items-center;
}

.content-wrapper {
	@apply flex flex-col;
}
