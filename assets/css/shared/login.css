.ant-input-affix-wrapper,
.ant-input {
	max-height: 40px !important;
}

#login_form_email {
	font-family: Inter;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;
}

#login_form_email::placeholder {
	color: var(--grey-500) !important;
}

:deep(.ant-form-item-label label) {
	line-height: 18px !important;
}

:deep(.ant-form-item-label) {
	height: 20px;
	padding-bottom: 0;
	margin-bottom: 6px;
}

:deep(.button-wrapper.simple:active) {
	background-color: var(--violet-700) !important;
}

.ant-form-item {
	margin-bottom: 20px;
}

:deep(.ant-input-affix-wrapper input::placeholder) {
	color: var(--grey-500) !important;
}

.login-button :deep(span) {
	font-size: 16px !important;
}

.auth-card {
	&-container {
		@apply flex h-full flex-col items-center justify-center bg-grey-50;
	}
}

:deep(.container-pwd-by) {
	@apply mt-6;
	@media theme(screens.smHeight.raw) {
		@apply -mt-4;
	}
}

:deep(.container-pwd-by.forgot-screen-pwd-by) {
	@media theme(screens.smHeight.raw) {
		@apply mt-6;
	}
}
