@import '~/assets/css/utils/helpers.css';
@import '~/assets/css/base/components.css';
@tailwind base;

@layer base {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	p,
	a,
	span {
		margin: 0;
		line-height: unset;
		color: var(--grey-700);
		font-family: 'Inter', sans-serif;
	}

	/** corrigir tamanhos em ordem h1,h2,h3,h4... **/

	h2 {
		@apply text-3xl;
	}

	h3 {
		@apply mb-0 text-2xl;
	}

	h4 {
		@apply mb-0 text-base;
	}

	h5 {
		@apply text-xl;
	}

	h6 {
		@apply text-sm;
	}

	b {
		@apply font-medium;
	}

	html,
	body {
		overflow: hidden;
		@apply !w-auto;
	}

	svg:focus {
		outline: none;
	}

	ul {
		padding-left: 0;
	}

	:root {
		/** base **/
		--white: #ffffff;
		--black: #000000;
		/** grey **/
		--grey-25: #f8f9fd;
		--grey-50: #f6f7fb;
		--grey-100: #eff1f7;
		--grey-200: #e7e8f0;
		--grey-300: #c7cbdd;
		--grey-400: #8d95b3;
		--grey-500: #5d6585;
		--grey-600: #424967;
		--grey-700: #313854;
		--grey-800: #1b2139;
		--grey-900: #0e1428;
		/** warning **/
		--warning-25: #fffcf5;
		--warning-50: #fffaeb;
		--warning-100: #fef0c7;
		--warning-200: #fedf89;
		--warning-300: #fec84b;
		--warning-400: #fdb022;
		--warning-500: #f79009;
		--warning-600: #dc6803;
		--warning-700: #b54708;
		--warning-800: #93370d;
		--warning-900: #7a2e0e;
		/** primary **/
		--primary-25: #f5faff;
		--primary-50: #eff8ff;
		--primary-100: #d1e9ff;
		--primary-200: #b2ddff;
		--primary-300: #84caff;
		--primary-400: #53b1fd;
		--primary-500: #2e90fa;
		--primary-600: #1570ef;
		--primary-700: #175cd3;
		--primary-800: #1849a9;
		--primary-900: #194185;
		/** error **/
		--error-25: #fffbfa;
		--error-50: #fef3f2;
		--error-100: #fee4e2;
		--error-200: #fecdca;
		--error-300: #fda29b;
		--error-400: #f97066;
		--error-500: #f04438;
		--error-600: #d92d20;
		--error-700: #b42318;
		--error-800: #912018;
		--error-900: #7a271a;
		/** success  **/
		--success-25: #f6fef9;
		--success-50: #ecfdf3;
		--success-100: #d1fadf;
		--success-200: #a6f4c5;
		--success-300: #6ce9a6;
		--success-400: #32d583;
		--success-500: #12b76a;
		--success-600: #039855;
		--success-700: #027a48;
		--success-800: #05603a;
		--success-900: #054f31;
		/** violet **/
		--violet-10: #f4ebff;
		--violet-25: #fbfaff;
		--violet-50: #f5f3ff;
		--violet-100: #ece9fe;
		--violet-200: #ddd6fe;
		--violet-300: #c3b5fd;
		--violet-400: #a48afb;
		--violet-500: #875bf7;
		--violet-600: #7839ee;
		--violet-700: #6927da;
		--violet-800: #5720b7;
		--violet-900: #491c96;
		/** pink **/
		--pink-25: #fff5f8;
		--pink-50: #ffebf2;
		--pink-100: #ffdbe8;
		--pink-200: #ffc2d8;
		--pink-300: #ff99be;
		--pink-400: #ff75a7;
		--pink-500: #ff4789;
		--pink-600: #ff186b;
		--pink-700: #d40851;
		--pink-800: #99063a;
		--pink-900: #73022b;
		/** cyan **/
		--cyan-25: #ebfcff;
		--cyan-50: #e1faff;
		--cyan-100: #aef1ff;
		--cyan-200: #76e7fe;
		--cyan-300: #43dffe;
		--cyan-400: #01d0fa;
		--cyan-500: #01b2d5;
		--cyan-600: #0198b7;
		--cyan-700: #017b93;
		--cyan-800: #005566;
		--cyan-900: #003f4c;
		/** sankhya colors **/
		--sankhya-green-25: #eff9ef;
		--sankhya-green-50: #66cc661a;
		--sankhya-green-600: #66cc66;
		--sankhya-green-700: #55ac55;
		/** sizes **/
		--left-sidebar-width: 296px;
		--left-short-side-bar: 52px;
		--navbar-height: 64px;
		/** shadows **/
		--shadow-base: 0px 8px 40px -8px rgba(16, 24, 40, 0.18);
		--shadow-base: 0px 1px 2px rgba(31, 41, 55, 0.08);
		--shadow-xs: 0px 1px 2px rgba(16, 24, 40, 0.05);
		--shadow-sm: 0px 1px 3px rgba(16, 24, 40, 0.1),
			0px 1px 2px rgba(16, 24, 40, 0.06);
		--shadow-md: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
			0px 2px 4px -2px rgba(16, 24, 40, 0.06);
		--shadow-lg: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
			0px 4px 6px -2px rgba(16, 24, 40, 0.03);
		--shadow-xl: 0px 20px 24px -4px rgba(16, 24, 40, 0.08),
			0px 8px 8px -4px rgba(16, 24, 40, 0.03);
		--shadow-2xl: 0px 24px 48px -12px rgba(16, 24, 40, 0.18);
		--shadow-3xl: 0px 32px 64px -12px rgba(16, 24, 40, 0.14);
		/** effects **/
		--focus-ring: 0px 0px 0px 4px #f4ebff;
		--to-violet-600: brightness(0) saturate(100%) invert(46%) sepia(76%)
			saturate(4462%) hue-rotate(236deg) brightness(98%) contrast(97%);
		--to-grey-800: brightness(0) saturate(100%) invert(11%) sepia(17%)
			saturate(1740%) hue-rotate(190deg) brightness(98%) contrast(95%);
		--to-grey-400: brightness(0) saturate(100%) invert(49%) sepia(6%) saturate(261%)
			hue-rotate(176deg) brightness(92%) contrast(91%);
	}
}

@tailwind components;
@tailwind utilities;
