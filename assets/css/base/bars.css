.navbar {
	@apply flex h-navbarHeight min-h-navbarHeight select-none items-center justify-between bg-white p-4;
	border-bottom: 1px solid var(--grey-200);
	box-shadow: 0px -2px 28px rgba(0, 0, 0, 0.02);
	z-index: 2;
}

.navbar--fit {
	@apply !h-[48px] !min-h-[48px];
}

.sidebar-right {
	border-right: 1px solid #e8e8e8;
	margin-top: 1px;
	box-shadow: 6px 4px 24px rgba(0, 0, 0, 0.05);
	z-index: 8;
	background: #ffffff;
	border-bottom: 1px solid var(--grey-200);
	&:first-of-type {
		box-shadow: -2px 1px 4px rgba(93, 101, 133, 0.1);
		border-left: 1px solid var(--grey-200);
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
	}
}

.home-sidebar-left {
	@apply w-projectsBarWidth min-w-projectsBarWidth bg-white;
	border-right: 1px solid var(--grey-200);
}

@keyframes colorTransition {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

.progress-bar {
	width: 100%;
	height: 3px;
	background: linear-gradient(90deg, #730eff 0%, rgba(1, 208, 250, 0.52) 100%);
	background-size: 200% 100%;
	animation-name: colorTransition;
	animation-duration: 2s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
	animation-direction: alternate;
	position: absolute;
	top: 0;
	z-index: 10;
}
