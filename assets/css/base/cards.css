@layer components {
	.auth-card {
		@apply flex flex-col items-center rounded-xl bg-white px-10 py-8;
		box-shadow: 0px 4px 8px -2px rgba(14, 20, 40, 0.1),
			0px 2px 4px -2px rgba(14, 20, 40, 0.06);
		min-width: 440px;
	}

	.auth-form {
		@media theme(screens.smHeight.raw) {
			@apply max-h-[360px] overflow-y-auto;
		}
	}

	.simple-square {
		@apply h-[40px] w-[40px];
	}

	.elevated-card {
		transition: all 0.3s ease;
		background: var(--white);
		cursor: pointer;
		border-radius: 8px;
		border: 1px solid var(--grey-200);
	}

	.icon-badge {
		border-radius: 8px;
		background-color: var(--violet-100);
		width: 52px;
		height: 52px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.elevated-card:hover {
		box-shadow: 0 0.48px 0.96px rgba(0, 0, 0, 0.2);
	}

	.new-skill-panel {
		@apply h-[80vh] overflow-auto;
	}
}
