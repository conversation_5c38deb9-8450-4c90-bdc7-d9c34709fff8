@layer components {
	.tapume-blured {
		background: rgba(255, 255, 255, 0.4);
		backdrop-filter: blur(8px);
		top: 68px;
		@apply pointer-events-none  absolute z-10 w-full overflow-hidden;
	}

	.horizontal-divider {
		@apply my-2 h-px w-full bg-grey-200;
	}

	.label-title {
		@apply text-sm font-medium text-grey-700 first-letter:uppercase;
	}

	.project-settings-popover {
		@apply w-[362px] !p-4 !py-6;
	}

	.simple-badge {
		@apply bg-grey-50 rounded-2xl px-2 text-grey-500 text-xs font-medium;
	}

	/* .vertical-custom-logo {
		max-width: 140px;
		width: auto !important;
		max-height: 36px;
	} */

	.custom-single-logo {
		width: auto !important;
		max-height: 52px;
		height: auto !important;
	}

	.our-ai-text {
		@apply mx-2 text-base font-normal text-grey-600 w-[721px] text-center mb-[32px];
	}

	.input-rename {
		@apply bg-[transparent];
		&:focus {
			border-color: var(--grey-300);
			box-shadow: none;
		}
		&:hover {
			border-color: var(--grey-300);
		}
	}

	.content-badge {
		width: 24px;
		height: 24px;
		display: flex;
		background-color: var(--grey-50);
		border-radius: 16px;
		justify-content: center;
		align-items: center;
		position: absolute;
		right: 10px;
	}

	.navbar-container .buttons-hover.button-wrapper__type-text {
		font-weight: unset !important;
		padding-left: 10px !important;
		&:hover {
			background-color: var(--grey-50) !important;
		}
	}

	.navbar-container .buttons-hover {
		padding-left: 10px !important;
	}

	.loading-row {
		@apply h-[72px] shrink-0 items-center flex;
		position: relative;
		overflow: hidden;
	}

	.loading-row::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(1px);
		z-index: 1;
	}

	.loading-row::after {
		content: '';
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: none;
		z-index: 2;
	}

	.prefixed-input {
		.ant-input-prefix {
			border-right: 1px solid var(--grey-300);
			margin-top: -4px;
			margin-bottom: -4px;
			padding-right: 8px;
		}
	}

	.rounded-icon-wrapper {
		background-color: var(--grey-50);
		padding: 6px;
		border-radius: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		> div {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: var(--grey-100);
			padding: 6px;
			border-radius: 100%;
		}
	}

	.filter-violet-600 {
		filter: var(--to-violet-600);
	}
}
