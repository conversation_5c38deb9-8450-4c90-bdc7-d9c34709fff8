.workspace-popover-menu {
	@apply w-[220px] rounded-lg capitalize;
}

.logged-user-settings {
	.list-tile {
		margin: 0 !important;
	}
}

.dropdown-list-item {
	@apply relative my-1.5 min-h-[32px] cursor-pointer select-none px-4 py-1.5 text-sm !text-grey-700 hover:bg-grey-50;
	white-space: nowrap;
	span {
		@apply text-sm text-grey-700;
	}
	svg {
		width: 16px;
		height: 16px;
	}
	> div {
		position: relative;
	}
	&--selected {
		@apply bg-grey-50;
		::before {
			content: '';
			position: absolute;
			right: 0;
			z-index: 2;
			display: inline-block;
			width: 20px;
			height: 20px;
			background-image: url('~~/assets/icons/check/grey_check.png');
			background-position: center;
			background-repeat: no-repeat;
		}
	}
}

.list-tile {
	@apply relative my-1 flex items-center px-2 py-1;
	box-sizing: border-box;
	transition: background-color 0.3s ease;
	&--loading {
		@apply !cursor-wait;
	}
	&:not(.list-tile--disabled) {
		@apply cursor-pointer;
	}
	&:hover:not(.list-tile--disabled) {
		@apply bg-grey-50;
	}
	&__sankhya:hover:not(.list-tile--disabled) {
		background-color: var(--sankhya-green-50) !important;
	}
	&--selected {
		@apply bg-grey-100;
	}
	&--selected__sankhya {
		background-color: var(--sankhya-green-50) !important;
		&:hover:not(.list-tile--disabled) {
			background-color: var(--sankhya-green-50) !important;
		}
		& .icon {
			color: var(--sankhya-green-600) !important;
		}
		& span {
			color: #55ac55 !important;
		}
	}
	&--disabled {
		& * {
			@apply !text-grey-300;
		}
		@apply !pointer-events-none;
	}
	&--show-tooltip {
		& * {
			@apply !text-grey-300;
		}
	}
}

.selectable {
	@apply cursor-pointer rounded-md px-2 hover:bg-grey-100;
}

.select-tile {
	@apply selectable my-1 flex;
}

.select-tile--disabled {
	@apply pointer-events-none text-grey-200;
}

.modal-footer {
	border-top: 1px solid #f3f3f3;
	@apply flex justify-end px-4 py-2;
}

.modal-form-body {
	@apply px-6 py-4;
}

.mitra-popover-wrapper {
	@apply rounded-md bg-white p-6;
	box-shadow: var(--shadow-xl);
	.ant-popover-inner {
		@apply shadow-none;
		&-content {
			@apply p-0;
		}
	}
}

.mitra-select-popover-wrapper {
	@apply rounded-md bg-white p-6;
	box-shadow: var(--shadow-xl);
	.ant-popover-inner {
		@apply shadow-none;
		&-content {
			@apply p-0;
		}
	}
}

.mitra-popover-wrapper.project-selector {
	> .ant-dropdown-content {
		background-color: var(--white);
		> .ant-dropdown-menu {
			background-color: transparent;
		}
	}
}

.sheet-format-dropdown {
	@apply rounded bg-white p-6;
	box-shadow: var(--shadow-md);
	border: 1px solid var(--grey-100);
}

.sheet-popover {
	.ant-popover-inner {
		@apply rounded;
		box-shadow: var(--shadow-md);
		border: 1px solid var(--grey-100);
	}
}
