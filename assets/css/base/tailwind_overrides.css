.prose pre {
	background-color: var(--grey-900);
	padding: 1rem;
	border-radius: 0.5rem;
	color: var(--white);
	font-size: 12px;
}

.prose pre code {
	background-color: transparent;
	font-size: 12px;
	color: var(--white);
}

/* Mantém a cor padrão para qualquer coisa dentro de <pre> <code> */
.prose pre code * {
	color: inherit !important;
}

/* Qualquer coisa dentro de .hljs-comment, incluindo ela mesma, fica vermelha */
.prose pre code .hljs-comment,
.prose pre code .hljs-comment * {
	color: var(--grey-300) !important;
	font-style: italic !important;
}

/* Evita que outros elementos fora de .hljs-comment sejam afetados */
.prose pre code *:not(.hljs-comment *):not(.hljs-comment) {
	color: inherit !important;
}

.prose code {
	background-color: #f3f4f6;
	padding: 0.2rem 0.4rem;
	border-radius: 0.25rem;
}
