/**
 * @class AuthService
 * Gerencia o ciclo de vida do token JWT, incluindo armazenamento, verificação,
 * renovação proativa e logout. Esta é uma implementação singleton.
 */
class AuthService {
	private tokenKey: string;
	private refreshBuffer: number;
	private refreshEndpoint: string;
	private refreshByTenantEndpoint: string;
	private loginPath: string;
	private isRefreshing: boolean = false;
	private subscribers: ((token: string) => void)[] = [];
	private renewalTimeoutId: NodeJS.Timeout | null = null;

	constructor() {
		// Valores padrão que podem ser sobrescritos por variáveis de ambiente se necessário
		this.tokenKey = 'token';
		this.refreshBuffer = 2 * 60 * 1000; // 2 minutos em milissegundos
		this.refreshEndpoint = 'mitraspace/user/refreshToken';
		this.refreshByTenantEndpoint = 'mitraspace/project/refreshedToken';
		this.loginPath = '/login';
	}

	/**
	 * Obtém o token de autenticação do localStorage.
	 * Garante que o token sempre tenha o prefixo 'Bearer '.
	 * @returns {string | null} O token JWT ou null se não existir.
	 */
	getToken(): string | null {
		if (typeof window === 'undefined') return null;
		const token = localStorage.getItem(this.tokenKey);
		if (!token) return null;
		return token.startsWith('Bearer ') ? token : `Bearer ${token}`;
	}

	/**
	 * Salva o token de autenticação no localStorage.
	 * @param {string} token - O token JWT a ser salvo.
	 */
	setToken(token: string): void {
		if (typeof window === 'undefined') return;

		if (!token.startsWith('Bearer ')) {
			token = `Bearer ${token}`;
		}

		localStorage.setItem(this.tokenKey, token);
		this.scheduleRenewal();
	}

	/**
	 * Remove o token de autenticação do localStorage.
	 */
	clearToken(): void {
		if (typeof window === 'undefined') return;
		localStorage.removeItem(this.tokenKey);
	}

	/**
	 * Decodifica o payload de um token JWT.
	 * @param {string} token - O token JWT.
	 * @returns {any | null} O payload decodificado ou null em caso de erro.
	 */
	private decodePayload(token: string): any | null {
		try {
			const payloadBase64 = token.split('.')[1];
			const decodedJson = atob(payloadBase64);
			return JSON.parse(decodedJson);
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error('Erro ao decodificar o token JWT:', error);
			return null;
		}
	}

	/**
	 * Verifica se o token está expirado.
	 * @returns {boolean} True se o token estiver expirado, não existir ou for inválido.
	 */
	isTokenExpired(): boolean {
		const token = this.getToken();
		if (!token) return true;

		const payload = this.decodePayload(token);
		if (!payload || typeof payload.exp !== 'number') {
			return true;
		}

		const expirationTime = payload.exp * 1000;

		// log actual token time
		// eslint-disable-next-line no-console
		console.log(`Token expira em: ${new Date(expirationTime).toLocaleString()}`);

		return Date.now() >= expirationTime;
	}

	/**
	 * Verifica se o token está próximo de expirar, com base no buffer configurado.
	 * @returns {boolean} True se o token estiver dentro do buffer de expiração.
	 */
	isTokenNearExpiry(): boolean {
		const token = this.getToken();
		if (!token) return false;

		const payload = this.decodePayload(token);
		if (!payload || typeof payload.exp !== 'number') {
			return false;
		}
		const expirationTime = payload.exp * 1000;
		const now = Date.now();
		return now < expirationTime && now >= expirationTime - this.refreshBuffer;
	}

	/**
	 * Executa o logout do usuário, limpando o token e redirecionando para a página de login.
	 */
	logout(): void {
		// eslint-disable-next-line no-console
		console.log(
			'%c[AuthService] Deslogando usuário. Token expirado ou inválido.',
			'color: #ff4d4f;'
		);
		this.clearToken();
		if (typeof window !== 'undefined') {
			window.location.href = this.loginPath;
		}
	}

	/**
	 * Cancela qualquer agendamento de renovação de token existente.
	 */
	private cancelScheduledRenewal(): void {
		if (this.renewalTimeoutId) {
			clearTimeout(this.renewalTimeoutId);
			this.renewalTimeoutId = null;
			// eslint-disable-next-line no-console
			console.log(
				'%c[AuthService] Agendamento de renovação proativa cancelado (renovação já ocorreu).',
				'color: #d4380d;'
			);
		}
	}

	/**
	 * Agenda uma renovação proativa para ocorrer um pouco antes do token expirar.
	 */
	scheduleRenewal(): void {
		this.cancelScheduledRenewal();

		const token = this.getToken();
		if (!token) return;

		const payload = this.decodePayload(token);
		if (!payload || typeof payload.exp !== 'number') {
			return;
		}

		const expirationTime = payload.exp * 1000;
		const now = Date.now();

		// Calcula o tempo até a janela de renovação começar
		const timeUntilRenewalWindow = expirationTime - now - this.refreshBuffer;

		// eslint-disable-next-line no-console
		console.log(
			`%c[AuthService] timeUntilRenewalWindow: ${timeUntilRenewalWindow}`,
			'color: #faad14;'
		);

		if (timeUntilRenewalWindow > 0) {
			// eslint-disable-next-line no-console
			console.log(
				`%c[AuthService] Renovação proativa agendada para daqui a ${Math.round(
					timeUntilRenewalWindow / 1000 / 60
				)} minutos.`,
				'color: #1890ff;'
			);

			this.renewalTimeoutId = setTimeout(async () => {
				// eslint-disable-next-line no-console
				console.log(
					'%c[AuthService] Timer de renovação proativa disparado. Verificando token...',
					'color: #13c2c2;'
				);
				// Verifica novamente, pois o usuário pode ter deslogado ou renovado manualmente
				if (this.getToken() && !this.isTokenExpired() && !this.isRefreshing) {
					await this.refreshToken();
				}
			}, timeUntilRenewalWindow);
		}
	}

	/**
	 * Adiciona uma requisição à fila para ser executada após a renovação do token.
	 * @param callback A função de callback que resolve a requisição original com o novo token.
	 */
	private addSubscriber(callback: (token: string) => void): void {
		this.subscribers.push(callback);
	}

	/**
	 * Executa todas as requisições na fila com o novo token.
	 * @param newToken O novo token JWT.
	 */
	private onRefreshed(newToken: string): void {
		this.subscribers.forEach((callback) => callback(newToken));
		this.subscribers = [];
	}

	/**
	 * Rotina de renovação do token.
	 * @returns Uma promessa que resolve com o novo token.
	 */
	async refreshToken({ tenantId }: { tenantId?: number } = {}): Promise<string> {
		// Se já existe uma rotina de refresh em andamento, retorna uma promessa
		// que será resolvida quando a rotina original terminar.
		if (this.isRefreshing) {
			return new Promise((resolve) => {
				this.addSubscriber((newToken: string) => {
					resolve(newToken);
				});
			});
		}

		this.isRefreshing = true;
		// eslint-disable-next-line no-console
		console.log(
			'%c[AuthService] Renovação de token iniciada...',
			'color: #faad14;'
		);
		this.cancelScheduledRenewal(); // Cancela o timer agendado, pois estamos renovando agora.

		try {
			const config = useRuntimeConfig();
			if (tenantId) {
				const response = await $fetch.raw<{ token: string }>(
					`${this.refreshByTenantEndpoint}/${tenantId}`,
					{
						method: 'GET',
						baseURL: config.public.API_BASE_URL,
						headers: {
							Authorization: `${this.getToken()}`
						}
					}
				);
				const tokenPayload = response._data;

				if (!tokenPayload || typeof tokenPayload !== 'object') {
					throw new Error('Token de renovação inválido ou não recebido.');
				}

				useAuthStore().setToken(tokenPayload as LoginPayloadResponse);
				this.setToken(tokenPayload.token);
				this.onRefreshed(tokenPayload.token);
				// eslint-disable-next-line no-console
				console.log(
					'%c[AuthService] Token de tenant renovado com sucesso.',
					'color: #52c41a;'
				);
				return tokenPayload.token;
			} else {
				const response = await $fetch.raw<{ token: string }>(this.refreshEndpoint, {
					method: 'GET',
					baseURL: config.public.API_BASE_URL,
					headers: {
						Authorization: `${this.getToken()}`
					}
				});

				// Assumindo que o backend retorna o novo token no corpo da resposta.
				// Adapte 'response._data.token' conforme a estrutura da sua API.
				const newToken = response._data?.token;
				if (!newToken || typeof newToken !== 'string') {
					throw new Error('Token de renovação inválido ou não recebido.');
				}

				this.setToken(newToken);
				this.onRefreshed(newToken);
				// eslint-disable-next-line no-console
				console.log('%c[AuthService] Token renovado com sucesso.', 'color: #52c41a;');
				return newToken;
			}
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error('[AuthService] Falha ao renovar o token:', error);
			this.logout(); // Desloga em caso de falha na renovação
			return Promise.reject(error);
		} finally {
			this.isRefreshing = false;
		}
	}
}

// Exporta uma instância única (singleton) da classe.
export const authService = new AuthService();
