/**
 * Enum para tipos de componentes de tela
 * Baseado nos valores definidos no ScreenComponent Java
 */
export enum ComponentTypeId {
  SELECTOR = 1,
  PIECHART = 2,
  BARCHART = 3,
  LABEL = 4,
  TABLE = 5,
  LINECHART = 6,
  MAP = 8,
  GAUGE = 9,
  DATAENTRY = 10,
  FOLDER = 11,
  CARDBOARD = 12
}

/**
 * Mapeamento de nomes legíveis para os tipos de componentes
 */
export const ComponentTypeNames: Record<ComponentTypeId, string> = {
  [ComponentTypeId.SELECTOR]: 'Selector',
  [ComponentTypeId.PIECHART]: 'Pie Chart',
  [ComponentTypeId.BARCHART]: 'Bar Chart',
  [ComponentTypeId.LABEL]: 'Label',
  [ComponentTypeId.TABLE]: 'Table',
  [ComponentTypeId.LINECHART]: 'Line Chart',
  [ComponentTypeId.MAP]: 'Map',
  [ComponentTypeId.GAUGE]: 'Gauge',
  [ComponentTypeId.DATAENTRY]: 'Data Entry',
  [ComponentTypeId.FOLDER]: 'Folder',
  [ComponentTypeId.CARDBOARD]: 'Cardboard'
};

/**
 * Função helper para obter o nome do tipo de componente
 */
export const getComponentTypeName = (typeId: ComponentTypeId): string => {
  return ComponentTypeNames[typeId] || 'Unknown';
};

/**
 * Função helper para verificar se um tipo de componente é válido
 */
export const isValidComponentType = (typeId: number): typeId is ComponentTypeId => {
  return Object.values(ComponentTypeId).includes(typeId as ComponentTypeId);
};
