name: Enviar solicitação GET para Vercel após merge em QA ou Release WebSummit

on:
  push:
    branches: [ qa, release#apas, release, beta, 'release-skw-homolog', 'preview', 'validacao', 'sankhya-preview', 'validacao2' ]
     

jobs:
  deploy-to-vercel-websummit:
    if: ${{ github.ref == 'refs/heads/release' }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout do código
      uses: actions/checkout@v2

    - name: Enviar solicitação GET para Vercel (deploy mitralab.io release)
      run: |
        curl -X GET \
          https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/psiciBc6gD

  deploy-to-vercel-validacao:
    if: ${{ github.ref == 'refs/heads/validacao' }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout do código
      uses: actions/checkout@v2

    - name: Enviar solicitação GET para Vercel (deploy mitralab.io validacao)
      run: |
        curl -X GET \
          https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/kPJvL1niXJ

  deploy-to-vercel-skw-preview:
    if: ${{ github.ref == 'refs/heads/sankhya-preview' }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout do código
      uses: actions/checkout@v2

    - name: Enviar solicitação GET para Vercel (deploy sankhya preview)
      run: |
        curl -X GET \
          https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/DLfQf3rNJL
          
  deploy-to-vercel-qa:
    if: ${{ github.ref == 'refs/heads/qa' }}
    runs-on: ubuntu-latest
        
    steps:
    - name: Checkout do código
      uses: actions/checkout@v2
        
    - name: Enviar solicitação GET para Vercel (deploy qa)
      run: |
        curl -X GET \
          https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/pGSmPV2bFa
  deploy-to-preview:
    if: ${{ github.ref == 'refs/heads/preview' }}
    runs-on: ubuntu-latest
        
    steps:
    - name: Checkout do código
      uses: actions/checkout@v2
        
    - name: Enviar solicitação GET para Vercel (deploy preview)
      run: |
        curl -X GET \
          https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/QTE9eQPYCy

  deploy-to-vercel-apas:
    if: ${{ github.ref == 'refs/heads/release#apas' }}
    runs-on: ubuntu-latest
        
    steps:
    - name: Checkout do código
      uses: actions/checkout@v2
        
    - name: Enviar solicitação GET para Vercel (deploy apas)
      run: |
        curl -X GET \
        https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/kqOELbjRyD

  deploy-to-vercel-ftv2:
    if: ${{ github.ref == 'refs/heads/release#fast-track-v2' }}
    runs-on: ubuntu-latest
        
    steps:
    - name: Checkout do código
      uses: actions/checkout@v2
        
    - name: Enviar solicitação GET para Vercel (ft-v2)
      run: |
        curl -X GET \
        https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/rKjKMz2Z89

  deploy-to-vercel-beta:
    if: ${{ github.ref == 'refs/heads/beta' }}
    runs-on: ubuntu-latest
        
    steps:
    - name: Checkout do código
      uses: actions/checkout@v2
        
    - name: Enviar solicitação GET para Vercel (BETA)
      run: |
        curl -X GET \
        https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/VoebBr1qNF

  deploy-to-vercel-validacao2:
    if: ${{ github.ref == 'refs/heads/validacao2' }}
    runs-on: ubuntu-latest
        
    steps:
    - name: Checkout do código
      uses: actions/checkout@v2
        
    - name: Enviar solicitação GET para Vercel (validacao 2)
      run: |
        curl -X GET \
        https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/kdYiOQxhA6

  deploy-to-vercel-skw-homolog:
    if: ${{ github.ref == 'refs/heads/release-skw-homolog' }}
    runs-on: ubuntu-latest
        
    steps:
    - name: Checkout do código
      uses: actions/checkout@v2
        
    - name: Enviar solicitação GET para Vercel (release-skw-homolog)
      run: |
        curl -X GET \
        https://api.vercel.com/v1/integrations/deploy/prj_Omdey5miXTyel3rzVAcWZCZryTtg/LxIfG5NVJf

    

          
